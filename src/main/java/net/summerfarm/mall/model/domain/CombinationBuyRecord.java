package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 组合购使用记录表
 * <AUTHOR>
 */
@Data
public class CombinationBuyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 组合购活动ID
     */
    private Long combinationBuyId;

    /**
     * 用户ID
     */
    private Long mId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 组合商品信息（JSON格式存储）
     */
    private String combinationSkus;

    /**
     * 原始总金额
     */
    private BigDecimal originalAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 优惠后金额
     */
    private BigDecimal finalAmount;

    /**
     * 使用时间
     */
    private LocalDateTime useTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
