package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 组合购商品池表
 * <AUTHOR>
 */
@Data
public class CombinationBuyPool implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 组合购活动ID
     */
    private Long combinationBuyId;

    /**
     * 商品池编号（A、B、C等）
     */
    private String poolCode;

    /**
     * 商品池名称
     */
    private String poolName;

    /**
     * 商品池描述
     */
    private String description;

    /**
     * 商品池顺序
     */
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记：0-正常，1-删除
     */
    private Integer delFlag;
}
