package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 组合购商品池SKU表
 * <AUTHOR>
 */
@Data
public class CombinationBuyPoolSku implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 组合购活动ID
     */
    private Long combinationBuyId;

    /**
     * 商品池ID
     */
    private Long poolId;

    /**
     * 商品池编号
     */
    private String poolCode;

    /**
     * 商品SKU
     */
    private String sku;

    /**
     * 商品名称（冗余字段，便于查询）
     */
    private String skuName;

    /**
     * 商品分类ID（冗余字段）
     */
    private Integer categoryId;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记：0-正常，1-删除
     */
    private Integer delFlag;
}
