package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 组合购活动表
 * <AUTHOR>
 */
@Data
public class CombinationBuy implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 折扣率（例如：0.7表示7折）
     */
    private BigDecimal discountRate;

    /**
     * 折扣金额（固定减免金额）
     */
    private BigDecimal discountAmount;

    /**
     * 优惠类型：1-折扣率，2-固定金额
     */
    private Integer discountType;

    /**
     * 商品池数量（2-3个）
     */
    private Integer poolCount;

    /**
     * 活动状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 适用区域（JSON格式存储区域编号列表）
     */
    private String applicableAreas;

    /**
     * 适用用户类型：0-全部，1-单店，2-大客户
     */
    private Integer userType;

    /**
     * 最小购买金额门槛
     */
    private BigDecimal minAmount;

    /**
     * 最大优惠金额限制
     */
    private BigDecimal maxDiscountAmount;

    /**
     * 每个用户限制使用次数（0表示不限制）
     */
    private Integer userLimitTimes;

    /**
     * 活动总限制使用次数（0表示不限制）
     */
    private Integer totalLimitTimes;

    /**
     * 已使用次数
     */
    private Integer usedTimes;

    /**
     * 优先级（数字越大优先级越高）
     */
    private Integer priority;

    /**
     * 是否可与其他优惠叠加：0-不可叠加，1-可叠加
     */
    private Integer stackable;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 删除标记：0-正常，1-删除
     */
    private Integer delFlag;
}
