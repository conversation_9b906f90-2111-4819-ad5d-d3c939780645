package net.summerfarm.mall.model.dto.market.activity;

import lombok.Data;
import net.summerfarm.mall.model.domain.market.ActivityBasicInfo;
import net.summerfarm.mall.model.domain.market.ActivityItemConfig;
import net.summerfarm.mall.model.domain.market.ActivitySkuDetail;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 组合购活动DTO（基于现有Activity表结构）
 * <AUTHOR>
 */
@Data
public class CombinationActivityDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动基本信息
     */
    private ActivityBasicInfo basicInfo;

    /**
     * 活动商品配置
     */
    private ActivityItemConfig itemConfig;

    /**
     * 商品池信息（按池子编号分组）
     */
    private Map<String, List<ActivitySkuDetail>> poolSkuMap;

    /**
     * 所有商品详情列表
     */
    private List<ActivitySkuDetail> skuDetails;

    /**
     * 商品池编号列表
     */
    private List<String> poolCodes;

    /**
     * 是否支持优惠券叠加
     */
    private Boolean couponStackable;

    /**
     * 定价类型（折扣或立减）
     */
    private Integer pricingType;

    /**
     * 定价类型描述
     */
    private String pricingTypeDesc;

    /**
     * 活动是否有效
     */
    private Boolean isValid;

    /**
     * 商品池数量
     */
    private Integer poolCount;

    /**
     * 总商品数量
     */
    private Integer totalSkuCount;

    public CombinationActivityDTO() {
    }

    public CombinationActivityDTO(ActivityBasicInfo basicInfo) {
        this.basicInfo = basicInfo;
        this.couponStackable = basicInfo.getCouponStackable() != null && basicInfo.getCouponStackable() == 1;
    }

    /**
     * 获取活动ID
     */
    public Long getActivityId() {
        return basicInfo != null ? basicInfo.getId() : null;
    }

    /**
     * 获取活动名称
     */
    public String getActivityName() {
        return basicInfo != null ? basicInfo.getName() : null;
    }

    /**
     * 检查活动是否有效
     */
    public boolean checkIsValid() {
        if (basicInfo == null) {
            return false;
        }

        // 检查活动状态
        if (basicInfo.getStatus() == null || basicInfo.getStatus() != 0) {
            return false;
        }

        // 检查活动时间
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        if (basicInfo.getStartTime() != null) {
            java.time.LocalDateTime startTime = basicInfo.getStartTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
            if (now.isBefore(startTime)) {
                return false;
            }
        }

        if (basicInfo.getEndTime() != null) {
            java.time.LocalDateTime endTime = basicInfo.getEndTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
            if (now.isAfter(endTime)) {
                return false;
            }
        }

        this.isValid = true;
        return true;
    }

    /**
     * 计算商品池数量
     */
    public void calculatePoolCount() {
        if (poolSkuMap != null) {
            this.poolCount = poolSkuMap.size();
        } else if (poolCodes != null) {
            this.poolCount = poolCodes.size();
        } else {
            this.poolCount = 0;
        }
    }

    /**
     * 计算总商品数量
     */
    public void calculateTotalSkuCount() {
        if (skuDetails != null) {
            this.totalSkuCount = skuDetails.size();
        } else if (poolSkuMap != null) {
            this.totalSkuCount = poolSkuMap.values().stream()
                    .mapToInt(List::size)
                    .sum();
        } else {
            this.totalSkuCount = 0;
        }
    }
}
