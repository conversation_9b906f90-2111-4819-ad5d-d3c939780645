package net.summerfarm.mall.model.dto.combination;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 组合购商品SKU DTO
 * <AUTHOR>
 */
@Data
public class CombinationBuySkuDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品SKU
     */
    private String sku;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品分类ID
     */
    private Integer categoryId;

    /**
     * 商品池编号
     */
    private String poolCode;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 是否已应用其他优惠
     */
    private Boolean hasOtherDiscount;

    /**
     * 其他优惠后的价格
     */
    private BigDecimal discountedPrice;

    public CombinationBuySkuDTO() {
    }

    public CombinationBuySkuDTO(String sku, String poolCode, Integer quantity) {
        this.sku = sku;
        this.poolCode = poolCode;
        this.quantity = quantity;
    }

    public CombinationBuySkuDTO(String sku, String poolCode, Integer quantity, BigDecimal price) {
        this.sku = sku;
        this.poolCode = poolCode;
        this.quantity = quantity;
        this.price = price;
        this.totalPrice = price != null ? price.multiply(BigDecimal.valueOf(quantity)) : BigDecimal.ZERO;
    }
}
