package net.summerfarm.mall.model.dto.combination;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 组合购方案DTO
 * <AUTHOR>
 */
@Data
public class CombinationPlanDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 方案ID（用于标识不同的组合方案）
     */
    private String planId;

    /**
     * 组合购活动ID
     */
    private Long combinationBuyId;

    /**
     * 参与组合的商品列表（按池子分组）
     */
    private Map<String, List<CombinationBuySkuDTO>> combinationSkusByPool;

    /**
     * 参与组合的商品列表（平铺）
     */
    private List<CombinationBuySkuDTO> combinationSkus;

    /**
     * 组合原价
     */
    private BigDecimal originalAmount;

    /**
     * 组合购优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 组合购优惠后金额
     */
    private BigDecimal discountedAmount;

    /**
     * 优惠率
     */
    private BigDecimal discountRate;

    /**
     * 是否满足组合购条件
     */
    private Boolean isValid = false;

    /**
     * 组合描述
     */
    private String description;

    /**
     * 方案优先级（优惠金额越大优先级越高）
     */
    private Integer priority;

    public CombinationPlanDTO() {
    }

    public CombinationPlanDTO(String planId, Long combinationBuyId) {
        this.planId = planId;
        this.combinationBuyId = combinationBuyId;
    }

    /**
     * 计算优惠率
     */
    public void calculateDiscountRate() {
        if (originalAmount != null && originalAmount.compareTo(BigDecimal.ZERO) > 0 && discountAmount != null) {
            this.discountRate = discountAmount.divide(originalAmount, 4, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 计算优惠后金额
     */
    public void calculateDiscountedAmount() {
        if (originalAmount != null && discountAmount != null) {
            this.discountedAmount = originalAmount.subtract(discountAmount);
        }
    }

    /**
     * 设置优先级（基于优惠金额）
     */
    public void setPriorityByDiscount() {
        if (discountAmount != null) {
            this.priority = discountAmount.multiply(BigDecimal.valueOf(100)).intValue();
        }
    }
}
