package net.summerfarm.mall.model.dto.combination;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 组合购计算响应DTO
 * <AUTHOR>
 */
@Data
public class CombinationCalculateRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否有可用的组合购优惠
     */
    private Boolean hasDiscount = false;

    /**
     * 使用的组合购活动ID
     */
    private Long combinationBuyId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 最优组合方案列表
     */
    private List<CombinationPlanDTO> optimalPlans;

    /**
     * 总原价
     */
    private BigDecimal originalTotalAmount;

    /**
     * 组合购优惠金额
     */
    private BigDecimal combinationDiscountAmount;

    /**
     * 组合购优惠后金额
     */
    private BigDecimal combinationDiscountedAmount;

    /**
     * 其他优惠金额（特价、优惠券等）
     */
    private BigDecimal otherDiscountAmount;

    /**
     * 最终金额（所有优惠后）
     */
    private BigDecimal finalAmount;

    /**
     * 总优惠金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 未参与组合购的商品列表
     */
    private List<CombinationBuySkuDTO> nonCombinationSkus;

    /**
     * 优惠详情说明
     */
    private String discountDescription;

    /**
     * 计算总优惠金额
     */
    public void calculateTotalDiscount() {
        BigDecimal combination = combinationDiscountAmount != null ? combinationDiscountAmount : BigDecimal.ZERO;
        BigDecimal other = otherDiscountAmount != null ? otherDiscountAmount : BigDecimal.ZERO;
        this.totalDiscountAmount = combination.add(other);
    }

    /**
     * 计算最终金额
     */
    public void calculateFinalAmount() {
        if (originalTotalAmount != null && totalDiscountAmount != null) {
            this.finalAmount = originalTotalAmount.subtract(totalDiscountAmount);
        }
    }
}
