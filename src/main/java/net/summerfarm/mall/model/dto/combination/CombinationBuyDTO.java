package net.summerfarm.mall.model.dto.combination;

import lombok.Data;
import net.summerfarm.mall.model.domain.CombinationBuyPool;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 组合购活动DTO
 * <AUTHOR>
 */
@Data
public class CombinationBuyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 折扣率
     */
    private BigDecimal discountRate;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 优惠类型：1-折扣率，2-固定金额
     */
    private Integer discountType;

    /**
     * 商品池数量
     */
    private Integer poolCount;

    /**
     * 活动状态
     */
    private Integer status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 适用区域列表
     */
    private List<Integer> applicableAreaList;

    /**
     * 适用用户类型
     */
    private Integer userType;

    /**
     * 最小购买金额门槛
     */
    private BigDecimal minAmount;

    /**
     * 最大优惠金额限制
     */
    private BigDecimal maxDiscountAmount;

    /**
     * 每个用户限制使用次数
     */
    private Integer userLimitTimes;

    /**
     * 活动总限制使用次数
     */
    private Integer totalLimitTimes;

    /**
     * 已使用次数
     */
    private Integer usedTimes;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 是否可与其他优惠叠加
     */
    private Integer stackable;

    /**
     * 商品池列表
     */
    private List<CombinationBuyPoolDTO> pools;

    /**
     * 是否活动有效
     */
    public boolean isValid() {
        if (status == null || status != 1) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        return (startTime == null || now.isAfter(startTime)) && 
               (endTime == null || now.isBefore(endTime));
    }

    /**
     * 是否达到使用次数限制
     */
    public boolean isReachLimit() {
        return totalLimitTimes != null && totalLimitTimes > 0 && 
               usedTimes != null && usedTimes >= totalLimitTimes;
    }
}
