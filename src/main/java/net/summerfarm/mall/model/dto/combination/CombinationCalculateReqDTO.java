package net.summerfarm.mall.model.dto.combination;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 组合购计算请求DTO
 * <AUTHOR>
 */
@Data
public class CombinationCalculateReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long mId;

    /**
     * 区域编号
     */
    private Integer areaNo;

    /**
     * 业务线
     */
    private Integer businessLine;

    /**
     * 购买的商品列表
     */
    @NotEmpty(message = "购买商品列表不能为空")
    private List<CombinationBuySkuDTO> skuList;

    /**
     * 是否计算最优组合（true-计算最优组合，false-按传入顺序计算）
     */
    private Boolean calculateOptimal = true;

    /**
     * 是否包含其他优惠（特价、优惠券等）
     */
    private Boolean includeOtherDiscounts = true;

    /**
     * 指定的组合购活动ID（可选，如果不指定则自动匹配最优活动）
     */
    private Long combinationBuyId;
}
