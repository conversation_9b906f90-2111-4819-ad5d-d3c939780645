package net.summerfarm.mall.model.dto.combination;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 组合购商品池DTO
 * <AUTHOR>
 */
@Data
public class CombinationBuyPoolDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品池ID
     */
    private Long id;

    /**
     * 组合购活动ID
     */
    private Long combinationBuyId;

    /**
     * 商品池编号
     */
    private String poolCode;

    /**
     * 商品池名称
     */
    private String poolName;

    /**
     * 商品池描述
     */
    private String description;

    /**
     * 商品池顺序
     */
    private Integer sortOrder;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 商品池中的SKU列表
     */
    private List<String> skuList;

    /**
     * 商品池中的SKU详情列表
     */
    private List<CombinationBuySkuDTO> skuDetails;
}
