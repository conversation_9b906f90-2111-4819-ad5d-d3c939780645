package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.crm.client.enums.BdQrCodeQueryChannelEnum;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.dto.login.LoginMerchantCacheDTO;
import net.summerfarm.mall.model.input.BdQrCodeQueryInput;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.popmall.PopMallWhitelistService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Package: net.summerfarm.customer.controller
 * @Description: 商户
 * @author: <EMAIL>
 * @Date: 2016/9/30
 */
@Api(tags = "商户控制类")
@RestController
@RequestMapping(value = "/merchant")
public class MerchantController{

    @Resource
    private MerchantService merchantService;
    @Resource
    private MemberService memberService;
    @Resource
    private MerchantCouponService merchantCouponService;
    @Resource
    private ContactService contactService;
    @Resource
    private CrmService crmService;
    @Resource
    private PopMallWhitelistService popMallWhitelistService;


    /**
     * 展示用户信息
     * @return
     */
    @ApiOperation(value = "展示用户信息",httpMethod = "GET",response = Merchant.class)
    @RequestMapping(method = RequestMethod.GET)
    public AjaxResult resubjectMerchant() {
        return merchantService.resubjectMerchant();
    }


    /**
     * 查询当前用户信息
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/query-merchant-info", method = RequestMethod.GET)
    public CommonResult<Merchant> queryMerchantInfo() {
        return CommonResult.ok(merchantService.queryMerchantInfo(RequestHolder.getMId()));
    }

    /**
     * 查询当前用户信息
     */
    @RequiresAuthority
    @RequestMapping(value = "/query/user-info", method = RequestMethod.POST)
    public CommonResult<LoginMerchantCacheDTO> getMerchantInfo() {
        return CommonResult.ok(RequestHolder.getLoginMerchantCache());
    }

    /**
     * 获取用户的渠道码
     * @return
     */
    @ApiOperation(value = "获取用户的渠道码",httpMethod = "GET")
    @RequiresAuthority
    @RequestMapping(value = "/channel-code")
    public AjaxResult channelCode() {
        return merchantService.channelCode(RequestHolder.getMId());
    }

    @ApiOperation(value = "查询用户邀请信息",httpMethod = "GET")
    @RequiresAuthority
    @RequestMapping(value = "/invite-info")
    public AjaxResult inviteInfo() {
        return merchantService.selectInviteInfo();
    }


    /**
     * 查询会员信息
     * @return
     */
    @ApiOperation(value = "查询会员信息",httpMethod = "GET")
    @RequiresAuthority
    @RequestMapping(value = "/member-select",method = RequestMethod.GET)
    public AjaxResult selectMember(){
        return memberService.selectMember();
    }

    /**
     * 查询会员极速退款订单
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "查询会员极速退款订单",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页数",paramType = "path",defaultValue = "1",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",defaultValue = "10",required = true)
    })
    @RequiresAuthority
    @RequestMapping(value = "/member/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    public AjaxResult selectOrders(@PathVariable int pageIndex, @PathVariable int pageSize){
        return memberService.selectOrders(pageIndex,pageSize);
    }
    /**
    * 用户每天第一次登录 记录登录时间
    */
    @RequestMapping(value = "/member/loginTime",method = RequestMethod.POST)
    public AjaxResult updateLoginTime(){
        return merchantService.updateLoginTime();
    }

    /**
    * 查询用户券信息(活动券，新人券，权益券)
    */
    @RequestMapping(value = "/member/coupon",method = RequestMethod.GET)
    public AjaxResult queryMerchantCoupon(){

        return merchantCouponService.queryMerchantCoupon();
    }

    /**
     * 参与团购
     */
    @RequestMapping(value = "/insert/groupPurchase",method = RequestMethod.GET)
    public AjaxResult inertGroupPurchase(){

        return merchantService.insertGroupPurchase();
    }


    @ApiOperation(value = "高德搜索周边",httpMethod = "GET",tags = "客户管理")
    @RequestMapping(value = "/queryMerchantAddress", method = RequestMethod.GET)
    public AjaxResult queryMerchantAddress( String keywords, String city){
        return AjaxResult.getOK(GaoDeUtil.searchAroundAPI(keywords, city, 10));
    }

    /**
     * 点击获取免配日的值
     */
    @GetMapping("/getFreeDay")
    public AjaxResult getFreeDay(){
        return merchantService.getFreeDay();
    }

    @RequiresAuthority
    @PostMapping("/query/bd-info")
    public AjaxResult queryBdInfo(){
        return merchantService.queryBdInfo();
    }


    /**
     * 根据客户信息动态查询对应bd的企微二维码
     * 根据不同渠道,所获得的二维码不同, 如: 注册页面,首页弹窗和个人中心按钮
     * 当类型为首页弹窗时,如果距离上次弹窗时间不满一个自然月时,则不弹出(返回Null)
     *
     * @param input 二维码渠道
     * @return 二维码链接
     */
    @PostMapping("/query/bd-qr")
    public CommonResult<String> queryBdQr(@RequestBody(required = false) BdQrCodeQueryInput input) {
        if (input == null) {
            input = new BdQrCodeQueryInput(BdQrCodeQueryChannelEnum.REGISTER);
        }
        return CommonResult.ok(crmService.queryBdQr(input));
    }

    /**
     * 根据客户信息动态返回该门店是否可以使用POP；
     * - 如果已经过了开市时间，则所有门店都可以使用；
     * - 如果未到开市时间，则只有白名单门店可以使用；
     *
     * @return 门店是否可以使用POP商城的结果(true or false)
     */
    @PostMapping("/query/canCurrentMerchantUsePop")
    public CommonResult<Boolean> canCurrentMerchantUsePop() {
        return CommonResult.ok(popMallWhitelistService.isCurrentMerchantCanUsePop());
    }

    /**
     * 催审接口
     */
    @PostMapping("/urge-audit")
    @RequiresAuthority
    public CommonResult<Boolean> urgeAudit() {
        return CommonResult.ok(merchantService.urgeAudit());
    }



}
