package net.summerfarm.mall.service;
import net.summerfarm.mall.model.domain.Refund;

public interface RefundService {
    void refundReal();

    /**
     * 退款成功后续处理
     * @param refund 退款信息
     */
    void afterRefund(Refund refund);


    /**
     * 退款成功回调处理
     * @param refund 退款信息
     */
    void notifySuccess(Refund refund);

    /**
     * 订单人工退款处理
     * @param orderNo orderNo
     */
    void laborRefund(String orderNo);
}
