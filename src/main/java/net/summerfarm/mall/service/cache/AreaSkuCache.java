package net.summerfarm.mall.service.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.mapper.AreaSkuMapper;
import net.summerfarm.mall.model.domain.AreaSku;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/8/26 17:38
 * @PackageName:net.summerfarm.mall.service.cache
 * @ClassName: AreaSkuCache
 * @Description: TODO
 * @Version 1.0
 */
@Component
@Slf4j
public class AreaSkuCache {

    @Resource
    private AreaSkuMapper areaSkuMapper;

    private final LoadingCache<Long, List<AreaSku>> AREA_SKU_CACHE = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .recordStats()
            // 设置 设定时间 后 刷新缓存
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, List<AreaSku>>() {
                @Override
                public List<AreaSku> load(Long pdId) {
                    return areaSkuMapper.getAreaSkuInfoByPdId(pdId);
                }
            });

    public List<AreaSku> getAreaSkuInfoByPdId(Long pdId) {
        List<AreaSku> areaSkuVOS = null;
        try {
            areaSkuVOS = AREA_SKU_CACHE.get(pdId);
        } catch (ExecutionException e) {
            log.warn("AreaSkuCache[]getAreaSkuInfoByPdId[]error[]pdId:{},cause:{}",pdId, e);
        }
        return areaSkuVOS;
    }
}
