package net.summerfarm.mall.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.mall.enums.ProductsRecommendEnum;
import net.summerfarm.mall.model.vo.CombinationSortVO;
import net.summerfarm.mall.model.vo.ProductInfoVO;

import java.util.List;

public interface ProductRecommendService {

    /**
     * 商品推荐查询接口-基于ES实现
     * @param pageIndex 页码
     * @param pageSize  分页大小
     * @param skuStr    sku（英文逗号分隔，传值时按sku推荐，为空时按用户推荐）
     * @param type      推荐类型（0、默认（首页） 1、商品详情页 2、购物车）
     * @param tabId     tabId
     * @return
     */
    PageInfo<ProductInfoVO> recommendV2(int pageIndex, int pageSize, String skuStr, Integer type, Integer tabId);

    /**
     * 查询所有tab
     * @return tabList
     */
    List<CombinationSortVO> listAllTab();

    List<String> getHomeRecommendSku();
}
