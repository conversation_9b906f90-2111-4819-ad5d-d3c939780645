package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.CouponTypeEnum;
import net.summerfarm.enums.InventoryAdjustPriceTypeEnum;
import net.summerfarm.enums.SkuTypeEnum;
import net.summerfarm.enums.TrolleyProductTypeEnum;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.bo.coupon.ReceiveIdCountBO;
import net.summerfarm.mall.model.converter.order.OrderItemConverter;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.model.dto.market.activity.TimingSkuActivityDTO;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponDTO;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponReqDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.LadderPriceVO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.model.vo.TimingRuleVO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.dto.order.UserContextParam;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.PricePreferentialVO;
import net.summerfarm.mall.model.vo.price.SkuPreferentialVO;
import net.summerfarm.mall.model.vo.price.TakeActualLadderPriceVO;
import net.summerfarm.mall.model.vo.price.TakeActualPriceVO;
import net.summerfarm.mall.order.AbstractOrderItemHandler;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import net.summerfarm.mall.order.OrderCalcFollowConfig;
import net.summerfarm.mall.order.converter.MerchantCouponConverter;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.helper.OrderCalcServiceHelper;
import net.summerfarm.mall.model.dto.combination.*;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 下单计算
 */
@Slf4j
@Service
public class OrderCalcServiceImpl extends BaseService<OrderCalcServiceImpl> implements OrderCalcService {
    @Resource
    private TrolleyService trolleyService;
    @Resource
    private MerchantCouponService merchantCouponService;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private CategoryService categoryService;
    @Resource
    private ProductService productService;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private MajorPriceService majorPriceService;
    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private OrderService orderService;
    @Resource
    private PrepayInventoryService prepayInventoryService;
    @Resource
    private AreaSkuService areaSkuService;
    @Lazy
    @Resource
    ActivityService activityService;
    @Resource
    private OrderCalcServiceHelper orderCalcServiceHelper;
    @Resource
    private CouponSenderService couponSenderService;
    @Resource
    private TimingRuleMapper timingRuleMapper;
    @Resource
    private DiscountCardToMerchantMapper discountCardToMerchantMapper;
    @Resource
    private DiscountCardMapper discountCardMapper;
    @Resource
    private CouponService couponService;
    @Resource
    private ConfigService configService;
    @Resource
    private MarketPriceControlProductsService marketPriceControlProductsService;
    @Resource
    private PriceStrategyService priceStrategyService;
    @Resource
    private CombinationBuyService combinationBuyService;


    /** 屏蔽sku */
    public static final String aolLimitPriceSku = "AOL_LIMIT_PRICE_SKU";

    /** 释放卡劵-可以给安佳类产品使用 */
    public static final String releaseCard = "RELEASE_CARD";

    @Override
    public List<TakeActualPriceVO> takePriceHandler(PlaceOrderVO placeOrderVO) {
        List<TakeActualPriceVO> takeActualPriceVOS = new ArrayList<>();
        if (ObjectUtils.isEmpty(placeOrderVO) || CollectionUtil.isEmpty(placeOrderVO.getOrderNow())){
            return takeActualPriceVOS;
        }
        if(RequestHolder.isMajor()){ //NOSONAR
            log.info("大客户不返回预估到手价信息");
            return takeActualPriceVOS;
        }

        placeOrderVO.setIsTakePrice(PlaceOrderPriceEnum.ISTAKEPRICE.getCode());

        //补充必要信息，注意initPlaceOrder不可修改，下单复用
        orderCalcServiceHelper.initPlaceOrder(placeOrderVO);

        return takePriceHandlerInternal(placeOrderVO);
    }

    @Override
    public List<TakeActualPriceVO> takePriceHandler(PlaceOrderVO placeOrderVO, UserContextParam userContext) {
        List<TakeActualPriceVO> takeActualPriceVOS = new ArrayList<>();
        if (ObjectUtils.isEmpty(placeOrderVO) || CollectionUtil.isEmpty(placeOrderVO.getOrderNow())) {
            return takeActualPriceVOS;
        }
        if (userContext.getMajorMerchant() != null && userContext.getMajorMerchant()) { //NOSONAR
            log.info("大客户不返回预估到手价信息");
            return takeActualPriceVOS;
        }

        placeOrderVO.setIsTakePrice(PlaceOrderPriceEnum.ISTAKEPRICE.getCode());

        //使用传入的用户上下文信息初始化订单
        initPlaceOrderWithUserContext(placeOrderVO, userContext);

        return takePriceHandlerInternal(placeOrderVO);
    }

    /**
     * 内部到手价计算方法，提取公共逻辑
     */
    private List<TakeActualPriceVO> takePriceHandlerInternal(PlaceOrderVO placeOrderVO) {
        List<TakeActualPriceVO> takeActualPriceVOS = new ArrayList<>();

        //获取需要获得到手价的sku
        List<Trolley> trolleyList = placeOrderVO.getOrderNow();

        //获取全部控价品信息
        Map<String, MarketPriceControlProducts> controlProductsMap = marketPriceControlProductsService.selectAllControlProductsByCache();

        //sku项构建：原价、营销策略
        List<OrderItemCalcDTO> itemCalcVOList = orderItemCacheBuild(trolleyList, placeOrderVO, controlProductsMap);
        if (CollectionUtils.isEmpty(itemCalcVOList)) {
            return takeActualPriceVOS;
        }

        //到手价计算
        if (null != placeOrderVO.getTimingSkuPrice() && placeOrderVO.getTimingSkuPrice() == 1){
            //省心送获取到手价
            for (OrderItemCalcDTO orderItemCalcDTO : itemCalcVOList) {
                this.getTimingSkuPrice(takeActualPriceVOS, orderItemCalcDTO, placeOrderVO);
            }
        }else {
            if (placeOrderVO.getTakePriceFlag()){ //NOSONAR
                //不均摊金额，获取明细
                for (OrderItemCalcDTO orderItemCalcDTO : itemCalcVOList){
                    //获取到手价
                    PlaceOrderCalcDTO takePriceCalcDTO = takePriceHandler(Collections.singletonList(orderItemCalcDTO), placeOrderVO);
                    this.getTakePriceVOList(takeActualPriceVOS,takePriceCalcDTO,placeOrderVO);
                }
            }else {
                //均摊金额，不获取明细
                PlaceOrderCalcDTO takePriceCalcDTO = takePriceHandler(itemCalcVOList, placeOrderVO);
                this.getTakePriceVOList(takeActualPriceVOS,takePriceCalcDTO,placeOrderVO);
            }
        }

        //选出最优的价格信息并校验控价线逻辑
        return chooseBestPrice(takeActualPriceVOS, placeOrderVO, controlProductsMap);
    }

    /**
     * 使用用户上下文参数初始化PlaceOrderVO
     */
    private void initPlaceOrderWithUserContext(PlaceOrderVO placeOrderVO, UserContextParam userContext) {
        //默认数据处理
        if (placeOrderVO.getOutTimes() == null) {
            placeOrderVO.setOutTimes(userContext.getOutTimes() != null ? userContext.getOutTimes() : 0);
        }
        if (placeOrderVO.getHelpOrder() == null) {
            placeOrderVO.setHelpOrder(userContext.getHelpOrder() != null ? userContext.getHelpOrder() : 0);
        }

        //用户数据：从用户上下文参数获取
        placeOrderVO.setMId(userContext.getMId());
        placeOrderVO.setAccountId(userContext.getAccountId());
        placeOrderVO.setMname(userContext.getMname());
        placeOrderVO.setMajorMerchant(userContext.getMajorMerchant());
        placeOrderVO.setAdminId(userContext.getAdminId());
        placeOrderVO.setDirect(userContext.getDirect());
        placeOrderVO.setSkuShow(userContext.getSkuShow());
        placeOrderVO.setOpenId(userContext.getOpenId());
        placeOrderVO.setServer(userContext.getServer());
        placeOrderVO.setBusinessLine(userContext.getBusinessLine());

        placeOrderVO.setSize(userContext.getMajorMerchant() != null && userContext.getMajorMerchant() ? Global.BIG_MERCHANT : Global.COMMON_MERCHANT);

        //设置用户区域信息
        placeOrderVO.setArea(userContext.getArea());

        //配送地址信息 - 通过orderCalcServiceHelper获取联系人信息
        Contact contact = null;
        if (userContext.getContactId() != null) {
            // 如果用户上下文中指定了contactId，优先使用
            contact = orderCalcServiceHelper.getContactById(userContext.getContactId());
        } else if (placeOrderVO.getContactId() != null) {
            // 如果订单中指定了contactId，使用订单中的
            contact = orderCalcServiceHelper.getContactById(placeOrderVO.getContactId());
        } else {
            // 查询默认地址
            contact = orderCalcServiceHelper.getDefaultContactByMid(userContext.getMId());
        }
        placeOrderVO.setContact(contact);

        log.info("使用用户上下文初始化订单信息：placeOrderVO：{}", JSON.toJSONString(placeOrderVO));
    }

    private void getTimingSkuPrice(List<TakeActualPriceVO> takeActualPriceVOS,OrderItemCalcDTO orderItemCalcDTO,PlaceOrderVO placeOrderVO){
        if (ObjectUtils.isEmpty(orderItemCalcDTO)){
            return;
        }
        boolean isCache = placeOrderVO.getTakePriceFlag();

        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Long mId = merchantSubject.getMerchantId();

        // 根据timingRuled得到对应的sku信息
        Integer timingRuleId = placeOrderVO.getTimingRuleId();
        if (timingRuleId == null) {
            log.info("timingRuleId为空！");
            return;
        }

        TimingRuleVO timingRuleVO;
        if (isCache) {
            timingRuleVO = orderService.getTimingRuleVOByCache(timingRuleId);
        } else {
            timingRuleVO = timingRuleMapper.selectByPrimaryKey(timingRuleId);
        }
        String timingSku = timingRuleVO.getTimingSku();

        int categoryId = timingRuleVO.getCategoryId();

        AreaSku areaSku = placeOrderVO.getAreaSkuMap().get(timingSku);
        if (Objects.isNull(areaSku) || !areaSku.getOnSale()) { //NOSONAR
            log.info("areaSku信息为空！timingSku:{}", timingSku);
            return;
        }

        List<SkuPreferentialVO> skuPreferentialVOS = new ArrayList<>();

        ActivitySkuDetailDTO activitySku = placeOrderVO.getActivitySkuDetailMap().get(timingSku);
        log.info("省心送到手价获取活动信息：activitySku->{}",JSON.toJSONString(activitySku));

        /*if(!ObjectUtils.isEmpty(areaSku)){
            String ladderPriceJson = areaSku.getLadderPrice();
            // 阶梯价处理
            if (StringUtils.isNotBlank(ladderPriceJson) || ObjectUtil.notEqual("[]",ladderPriceJson)) {
                List<LadderPriceVO> ladderPriceObjs = JSONObject.parseArray(ladderPriceJson, LadderPriceVO.class);
                timingProduct.setLadderPrices(ladderPriceObjs);
            }
        }*/

        //根据quantity，timingRule得到该用户得到的售卖的梯度单价，总价
        Area area = merchantSubject.getArea();
        Integer quantity = orderItemCalcDTO.getAmount();
        //TimingRuleVO timingRule = timingRuleMapper.selectByPrimaryKey(timingRuleId);

        BigDecimal price = timingRuleVO.getSalePrice().setScale(2,BigDecimal.ROUND_HALF_UP);

        //未扣除优惠前总价
        BigDecimal orginTotalPrice = price.multiply(BigDecimal.valueOf(quantity)).setScale(2,BigDecimal.ROUND_HALF_UP);

        //有活动价取活动价
        if (activitySku != null && Objects.equals(activitySku.getIsSupportTiming(), 1)
                && orderItemCalcDTO.getActivityPrice() != null && price.compareTo(orderItemCalcDTO.getActivityPrice()) >= 0) {
            TimingSkuActivityDTO timingSkuActivityDTO = activityService.getTimingSkuActivityPrice(activitySku, isCache, quantity, timingSku, orderItemCalcDTO.getActivityPrice(), price);
            if (timingSkuActivityDTO != null && timingSkuActivityDTO.getTotalPriceAfterDiscount().compareTo(BigDecimal.ZERO) > 0) {
                if (placeOrderVO.getTakePriceFlag()) { //NOSONAR
                    //到手价明细返回
                    SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(OrderPreferentialTypeEnum.ACTIVITY, orginTotalPrice.subtract(timingSkuActivityDTO.getTotalPriceAfterDiscount()));
                    skuPreferentialVO.setValue(orderItemCalcDTO.getActivityPrice());
                    skuPreferentialVOS.add(skuPreferentialVO);
                }

                //活动优惠后的商品总价
                orginTotalPrice = timingSkuActivityDTO.getTotalPriceAfterDiscount();

                //活动优惠后的商品单价
                price = orginTotalPrice.divide(BigDecimal.valueOf(quantity), 2, RoundingMode.HALF_UP);
            }
        }

        //若是没有梯度价或者特价则用正常商品价格
        /*if(Objects.isNull(price)){
            price = timingProduct.getSalePrice().setScale(2,BigDecimal.ROUND_HALF_UP);
        }*/
        //未扣除优惠前总价
        //BigDecimal orginPrice = price.multiply(BigDecimal.valueOf(quantity)).setScale(2,BigDecimal.ROUND_HALF_UP);

        // 黄金卡优惠金额
        BigDecimal cardFee = BigDecimal.ZERO;
        ArrayList<OrderItem> orderItemList = new ArrayList<>();
        OrderItem timingOrderItem = new OrderItem();
        timingOrderItem.setAmount(quantity);
        timingOrderItem.setSku(timingSku);
        timingOrderItem.setPrice(price);
        timingOrderItem.setProductType(TrolleyProductTypeEnum.NORMAL.ordinal());
        orderItemList.add(timingOrderItem);

        List<DiscountCardUseRecord> useRecords = trolleyService.discountCardUsable(orderItemList, area.getAreaNo(), null , mId);
        if(!CollectionUtils.isEmpty(useRecords)){
            Integer discountCardMerchantId = useRecords.get(0).getDiscountCardMerchantId();
            DiscountCardToMerchant discountCardToMerchant = discountCardToMerchantMapper.selectByPrimaryKey(discountCardMerchantId);
            DiscountCard discountCard = discountCardMapper.selectByPrimaryKey(discountCardToMerchant.getDiscountCardId());
            BigDecimal cardPrice = discountCard.getDiscount();
            int sum = useRecords.stream().mapToInt(DiscountCardUseRecord::getUseTimes).sum();
            cardFee = BigDecimal.valueOf(sum).multiply(cardPrice).setScale(2,BigDecimal.ROUND_HALF_UP);

            if (placeOrderVO.getTakePriceFlag()){ //NOSONAR
                //到手价明细返回
                    SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(OrderPreferentialTypeEnum.DISCOUNT_CARD, cardFee);
                    //优惠卡这里固定是1
                    skuPreferentialVO.setRuleLevel(BigDecimal.ONE);
                    skuPreferentialVO.setValue(discountCard.getDiscount());
                skuPreferentialVOS.add(skuPreferentialVO);
            }
        }

        //应付价
        BigDecimal goodsPrice = orginTotalPrice.subtract(cardFee).setScale(2,BigDecimal.ROUND_HALF_UP);

        // 可用优惠券
        List<MerchantCouponVO> usableRuleCoupon = new ArrayList<>();
        BigDecimal timingCouponMoney = BigDecimal.ZERO;
        CommonResult<List<SkuCouponDTO>> listCommonResult;
        if (isCache) {
            List<Coupon> coupons = couponSenderService.listUsableCouponByCache(mId);
            listCommonResult = couponSenderService.listAllValidCouponFromCacheV2 (Collections.singletonList(orderItemCalcDTO), coupons);
        } else {
            SkuCouponReqDTO skuCouponReqDTO = new SkuCouponReqDTO();
            List<String> skuList =  Arrays.asList(timingSku);
            skuCouponReqDTO.setSkus(skuList);
            listCommonResult = couponSenderService.listAllValidCouponFromCache (skuCouponReqDTO);
        }
        List<MerchantCouponVO> resultCouponList = new ArrayList<>();
        //可领取的优惠券
        List<SkuCouponDTO> skuCouponList =  listCommonResult.getData();
        if (!CollectionUtils.isEmpty(skuCouponList)){
            List<Coupon> distinctSortedCoupons = skuCouponList.stream()
                    .flatMap(skuCoupon -> skuCoupon.getCouponList().stream())
                    .filter(e -> e.getEffectiveNum() == CouponEffectiveNumEnum.UNCLAIMED.getCode())
                    .collect(Collectors.toList());

            //查询手动领取的情况下用户领取卡劵的次数
            if (!CollectionUtil.isEmpty(distinctSortedCoupons)) {
                List<Integer> couponIdList = distinctSortedCoupons.stream().map(Coupon::getId).collect(Collectors.toList());
                Map<Integer, ReceiveIdCountBO> countBOMap;
                List<ReceiveIdCountBO> receiveIdCountBos;
                if (isCache) {
                    countBOMap = new HashMap<>(couponIdList.size());
                    receiveIdCountBos = merchantCouponService.getUserReceiveCountByCache(mId, CouponReceiveTypeEnum.RECEIVE.getCode());
                    Map<Integer, ReceiveIdCountBO> receiveIdCountBOMap = receiveIdCountBos.stream().collect(Collectors.toMap(x -> x.getCouponId(),
                            Function.identity(), (p1, p2) -> p2));
                    couponIdList.forEach(couponId -> {
                        if (!CollectionUtils.isEmpty(receiveIdCountBOMap) && receiveIdCountBOMap.containsKey(couponId)) {
                            countBOMap.put(couponId, receiveIdCountBOMap.get(couponId));
                        }
                    });
                } else {
                    receiveIdCountBos = merchantCouponMapper.getUserReceiveCount(couponIdList, mId, CouponReceiveTypeEnum.RECEIVE.getCode());
                    countBOMap = receiveIdCountBos.stream().collect(Collectors.toMap(x -> x.getCouponId(), Function.identity(), (p1, p2) -> p2));
                }

                Iterator<Coupon> couponIterator = distinctSortedCoupons.iterator();
                while (couponIterator.hasNext()) {
                    Coupon coupon = couponIterator.next();
                    //查询手动领取的情况下用户领取卡劵的次数
                    //1、限制领取次数的情况下判断次数是否用完
                    if (coupon.getQuantityClaimed() > 0) {
                        ReceiveIdCountBO receiveIdCountBO = countBOMap.get(coupon.getId());
                        if (Objects.nonNull(receiveIdCountBO) && receiveIdCountBO.getNum() >= coupon.getQuantityClaimed()) {
                            couponIterator.remove();
                            continue;
                        }
                    }

                    //2、限制张数情况下判断余量是否充足
                    if (coupon.getGrantLimit() > 0 && coupon.getGrantAmount() <= 0) {
                        couponIterator.remove();
                        continue;
                    }
                }

                resultCouponList = distinctSortedCoupons.stream().map(MerchantCouponConverter::converterMerchantCoupon).collect(Collectors.toList());
            }
        }

        //用户卡包可用所有优惠券
        List<MerchantCouponVO> usableCouponByCache = merchantCouponService.getUsableCouponByCache(placeOrderVO.getMId());

        List<MerchantCouponVO> timingRuleCoupon;
        if (isCache) {
            timingRuleCoupon = usableCouponByCache.stream().filter(merchantCouponVO -> merchantCouponVO.getAgioType().equals(CouponTypeEnum.NORMAL.getType()))
                    .collect(Collectors.toList());
        } else {
            timingRuleCoupon = merchantCouponMapper.selectTimingByMid(NumberUtils.INTEGER_ZERO,RequestHolder.getMId(), NumberUtils.INTEGER_ZERO);
        }

        //用户可用
        if (!CollectionUtils.isEmpty(timingRuleCoupon)){
            timingRuleCoupon.stream().forEach(e -> {
                e.setEffectiveNum(CouponEffectiveNumEnum.RECEIVE.getCode());
            });
        }
        if (!CollectionUtils.isEmpty(resultCouponList)){
            timingRuleCoupon.addAll(resultCouponList);
        }

        timingRuleCoupon = timingRuleCoupon.stream()
                .filter(t -> Objects.equals(CouponActivityScopeEnum.ALL.ordinal(),t.getActivityScope())
                        || Objects.equals(CouponActivityScopeEnum.TIMING_DELIVERY.ordinal(),t.getActivityScope())).collect(Collectors.toList());

        //查询sku屏蔽名单
        List<String> blackSkus = configService.getValuesWithCache(aolLimitPriceSku, 30 * 60L);

        //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
        List<String> releaseCardList = configService.getValuesByLocalCache(releaseCard);
        List<Integer> releaseCards = releaseCardList.stream().map(e -> Integer.valueOf(e)).collect(Collectors.toList());

        //到手价查询需要剔除的优惠券类型
        List<String> values = configService.getValues(Global.HIDE_COUPON_GROUP);
        Set<Integer> hideCouponGrouping = values.stream().map(Integer::valueOf).collect(Collectors.toSet());

        for (MerchantCouponVO couponVO : timingRuleCoupon){
            Map<Integer, Set<String>> couponBlackAndWhiteMap = couponService.getCouponBlackAndWhiteMap (couponVO.getCouponId ());
            Set<String> black = couponBlackAndWhiteMap.getOrDefault (BlackAndWhiteTypeEnum.BLACK.getCode (),Collections.emptySet ());
            Set<String> white = couponBlackAndWhiteMap.getOrDefault (BlackAndWhiteTypeEnum.WHITE.getCode (),Collections.emptySet ());

            //预估到手价屏蔽售后补偿券 需求名称：商城-预估到手价剔除售后补偿券 by[薄荷]
            if (!CollectionUtils.isEmpty(hideCouponGrouping) && hideCouponGrouping.contains(couponVO.getGrouping())) {
                log.info("优惠券：" + couponVO.getCouponId () + " ，省心送商品预估到手价剔除售后补偿券");
                continue;
            }

            Set<String> usableSku;
            if (!StringUtils.isEmpty(couponVO.getSku()) && !Objects.equals("{}", couponVO.getSku())) {
                usableSku = ((Map<String, String>) JSON.parse(couponVO.getSku())).keySet();
                if (!usableSku.contains(timingSku)){
                    continue;
                }
            }
            if (!CollectionUtils.isEmpty (white) && !white.contains(timingSku)) {
                continue;
            }

            if (!CollectionUtils.isEmpty(black) && black.contains(timingSku)) {
                continue;
            }

            //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
            if (CollectionUtils.isEmpty(releaseCards) || !releaseCards.contains(couponVO.getGrouping())) {

                //过滤安佳类产品不可用
                if (!CollectionUtils.isEmpty(blackSkus) && blackSkus.contains(timingSku)) {
                    continue;
                }
            }

            //如果优惠券上配置的类目，需要匹配是否可用
            String categoryIdStr = couponVO.getCategoryId();
            if (StringUtils.isNotBlank(categoryIdStr) && !Objects.equals(categoryIdStr, "{}")) {
                List<Integer> categoryIds = JSON.parseObject(categoryIdStr).keySet().stream()
                        .map(Integer::valueOf).collect(Collectors.toList());

                //将一级类目、二级、三级 全部转换成三级类目
                List<Integer> cacheSublevelCategoryIds = categoryService.getCacheSublevelCategoryIds(couponVO.getCouponId().longValue(), categoryIds);
                if (!CollectionUtils.isEmpty(cacheSublevelCategoryIds) && !cacheSublevelCategoryIds.contains(categoryId)) {
                    continue;
                }
            }

            usableRuleCoupon.add(couponVO);
        }

        //获取核心品保底价
        BigDecimal floorPrice = orderItemCalcDTO.getFloorPrice();

        TakeActualPriceVO takeActualPriceVO = new TakeActualPriceVO();
        //使用优惠券进行扣减
        if (!CollectionUtils.isEmpty(usableRuleCoupon)){
            BigDecimal finalGoodsPrice = goodsPrice;
            List<MerchantCouponVO> distinctSortedCoupons = usableRuleCoupon.stream()
                    .filter(e -> finalGoodsPrice.compareTo(e.getMoney()) > 0 && finalGoodsPrice.compareTo(e.getThreshold()) > 0)
                    .distinct()
                    .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed())
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(distinctSortedCoupons)){
                if (floorPrice != null && floorPrice.compareTo(BigDecimal.ZERO) > 0) {
                    for (MerchantCouponVO couponVO : distinctSortedCoupons) {
                        timingCouponMoney = couponVO.getMoney();
                        BigDecimal individualDiscount = goodsPrice.subtract(timingCouponMoney).divide(new BigDecimal(quantity), 2, RoundingMode.FLOOR);
                        if (individualDiscount.compareTo(floorPrice) < 0) {
                            log.info("优惠券均摊后价格低于低价，将剔除当前优惠券 couponId:{}, individualDiscount:{}, floorPrice:{}", couponVO.getCouponId(), individualDiscount, floorPrice);
                            continue;
                        }
                        goodsPrice = goodsPrice.subtract(timingCouponMoney);
                        takeActualPriceVO.setCouponId(couponVO.getCouponId());
                        if (Objects.nonNull(couponVO.getCouponSenderId())) {
                            takeActualPriceVO.setCouponSenderId(couponVO.getCouponSenderId());
                        }
                        takeActualPriceVO.setEffectiveNum(couponVO.getEffectiveNum());
                        if (placeOrderVO.getTakePriceFlag()) { //NOSONAR
                            //到手价明细返回
                            SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(OrderPreferentialTypeEnum.COUPON, timingCouponMoney);
                            skuPreferentialVO.setRuleLevel(couponVO.getThreshold());
                            skuPreferentialVOS.add(skuPreferentialVO);
                        }
                        break;
                    }
                } else {
                    MerchantCouponVO couponVO = distinctSortedCoupons.get(0);
                    timingCouponMoney = couponVO.getMoney();
                    goodsPrice = goodsPrice.subtract(timingCouponMoney);
                    takeActualPriceVO.setCouponId(couponVO.getCouponId());
                    if (Objects.nonNull(couponVO.getCouponSenderId())) {
                        takeActualPriceVO.setCouponSenderId(couponVO.getCouponSenderId());
                    }
                    takeActualPriceVO.setEffectiveNum(couponVO.getEffectiveNum());
                    if (placeOrderVO.getTakePriceFlag()) { //NOSONAR
                        //到手价明细返回
                        SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(OrderPreferentialTypeEnum.COUPON, timingCouponMoney);
                        skuPreferentialVO.setRuleLevel(couponVO.getThreshold());
                        skuPreferentialVOS.add(skuPreferentialVO);
                    }
                }
            }
        }

        BigDecimal rpCouponFee = BigDecimal.ZERO;
        Map<String,Object> selectKeys = new HashMap<>();
        selectKeys.put("mId", RequestHolder.getMId());
        selectKeys.put("vaildDateStart", new Date());
        selectKeys.put("used", 0);

        List<MerchantCouponVO> couponVOList;
        if (isCache) {
            //获取用户所有可以红包，再根据金额进行过滤出可以的红包
            BigDecimal finalTotal = goodsPrice;
            couponVOList = usableCouponByCache.stream().filter(merchantCouponVO -> merchantCouponVO.getAgioType().equals(CouponTypeEnum.RED_PACKET.getType()) &&
                            merchantCouponVO.getMoney().compareTo(finalTotal) <= 0 && merchantCouponVO.getThreshold().compareTo(finalTotal) <= 0 )
                    .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed()).collect(Collectors.toList());
        } else {
            couponVOList = merchantCouponMapper.selectUsableCoupon(CouponTypeEnum.RED_PACKET.getType(), placeOrderVO.getMId(), goodsPrice);
        }

        //加载用户红包
        Map<Integer, BigDecimal> couponMoneyMap = couponVOList.stream().collect(Collectors.toMap(MerchantCouponVO::getId, MerchantCouponVO::getMoney));
        Integer redUsableCouponId = null;
        if (placeOrderVO.getIsTakePrice().equals(PlaceOrderPriceEnum.ISTAKEPRICE.getCode())) {
            if (!CollectionUtils.isEmpty(couponVOList)){
                List<MerchantCouponVO> sortedList = couponVOList.stream()
                        .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed())
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(blackSkus) || !blackSkus.contains(timingSku)) {
                    placeOrderVO.setMerchantCouponId(new HashSet<>(Collections.singletonList(sortedList.get(0).getId())));
                    redUsableCouponId = sortedList.get(0).getId();
                }
            }
        }

        //红包
        if (redUsableCouponId != null && !CollectionUtils.isEmpty(couponMoneyMap) &&
                couponMoneyMap.containsKey(redUsableCouponId)) {
            rpCouponFee = couponMoneyMap.get(redUsableCouponId);

            if (!RequestHolder.isMajor()) { //NOSONAR
                if (goodsPrice.compareTo(rpCouponFee) <= 0) {
                    log.info("红包优惠金额大或等于0，所以不参与红包优惠！redUsableCouponId:{}", redUsableCouponId);
                } else {
                    goodsPrice = goodsPrice.subtract(rpCouponFee);
                    if (placeOrderVO.getTakePriceFlag()) { //NOSONAR
                        //到手价明细返回
                        SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(OrderPreferentialTypeEnum.RED_PACKET, rpCouponFee);
                        skuPreferentialVO.setValue(rpCouponFee);
                        skuPreferentialVOS.add(skuPreferentialVO);
                    }
                }
            }
        }

        if (!CollectionUtils.isEmpty(skuPreferentialVOS)){
            //sku到手价优惠价格
            PricePreferentialVO skuPreferentialPrice = new PricePreferentialVO();
            BigDecimal priceTotalAmount = BigDecimal.ZERO;
            List<SkuPreferentialVO> priceSkuList = new ArrayList<>();
            //sku到手价优惠活动
            PricePreferentialVO skuPreferentialActive = new PricePreferentialVO();
            BigDecimal activeTotalAmount = BigDecimal.ZERO;
            List<SkuPreferentialVO> activeSkuList = new ArrayList<>();
            //sku到手价优惠卡券
            PricePreferentialVO skuPreferentialCoupon = new PricePreferentialVO();
            BigDecimal couponTotalAmount = BigDecimal.ZERO;
            List<SkuPreferentialVO> couponSkuList = new ArrayList<>();
            for (SkuPreferentialVO skuPreferentialVO : skuPreferentialVOS){
                //优惠价格
                if (skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.ACTIVITY.ordinal()) || skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.LADDER.ordinal())
                        || skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.LIVE_BROADCAST.ordinal()) || skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.MALL_AND_LIVE.ordinal())){
                    priceTotalAmount = priceTotalAmount.add(skuPreferentialVO.getAmount().divide(BigDecimal.valueOf(quantity),2,RoundingMode.HALF_UP));
                    priceSkuList.add(skuPreferentialVO);
                }
                //优惠活动
                if (skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.FULL_REDUCE.ordinal())){
                    activeTotalAmount = activeTotalAmount.add(skuPreferentialVO.getAmount().divide(BigDecimal.valueOf(quantity),2,RoundingMode.HALF_UP));
                    activeSkuList.add(skuPreferentialVO);
                }
                //优惠卡券
                if (skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.DISCOUNT_CARD.ordinal()) || skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.COUPON.ordinal())
                        || skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.RED_PACKET.ordinal())){
                    couponTotalAmount = couponTotalAmount.add(skuPreferentialVO.getAmount().divide(BigDecimal.valueOf(quantity),2,RoundingMode.HALF_UP));
                    couponSkuList.add(skuPreferentialVO);
                }
            }
            if (!CollectionUtils.isEmpty(priceSkuList)){
                skuPreferentialPrice.setTotalAmount(priceTotalAmount);
                skuPreferentialPrice.setSkuPreferentialList(priceSkuList);
                takeActualPriceVO.setSkuPreferentialPrice(skuPreferentialPrice);
            }

            if (!CollectionUtils.isEmpty(activeSkuList)){
                skuPreferentialActive.setTotalAmount(activeTotalAmount);
                skuPreferentialActive.setSkuPreferentialList(activeSkuList);
                takeActualPriceVO.setSkuPreferentialActive(skuPreferentialActive);
            }

            if (!CollectionUtils.isEmpty(couponSkuList)){
                skuPreferentialCoupon.setTotalAmount(couponTotalAmount);
                skuPreferentialCoupon.setSkuPreferentialList(couponSkuList);
                takeActualPriceVO.setSkuPreferentialCoupon(skuPreferentialCoupon);
            }

        }

        if (goodsPrice.compareTo(timingRuleVO.getSalePrice().multiply(BigDecimal.valueOf(quantity))) == 0){
            //这里判断到手价如果和原价相同的话不返回
            return;
        }

        if (goodsPrice.divide(BigDecimal.valueOf(quantity),2,RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO) <= 0){
            //这里判断到手价如果小于0那么不返回
            return;
        }

        takeActualPriceVO.setSku(timingSku);
        takeActualPriceVO.setPrice(timingRuleVO.getSalePrice());
        takeActualPriceVO.setQuantity(quantity);
        takeActualPriceVO.setTakeActualPrice(goodsPrice.divide(BigDecimal.valueOf(quantity),2,RoundingMode.HALF_UP));
        takeActualPriceVOS.add(takeActualPriceVO);

    }

    private void getTakePriceVOList(List<TakeActualPriceVO> takeActualPriceVOS,PlaceOrderCalcDTO takePriceCalcDTO,PlaceOrderVO placeOrderVO){
         if (ObjectUtils.isEmpty(takePriceCalcDTO) || takePriceCalcDTO.getItemCalcDTOList().isEmpty()){
             return;
         }
        List<OrderItemCalcDTO> itemCalcDTOList =   takePriceCalcDTO.getItemCalcDTOList();
         for (OrderItemCalcDTO itemCalcDTO : itemCalcDTOList){
             if (itemCalcDTO.getPrice().multiply(BigDecimal.valueOf(itemCalcDTO.getAmount())).compareTo(itemCalcDTO.getActualTotalPrice()) == 0){
                //这里判断到手价如果和原价相同的话不返回
                 continue;
             }
             TakeActualPriceVO takeActualPriceVO = new TakeActualPriceVO();
             takeActualPriceVO.setSku(itemCalcDTO.getSku());
             takeActualPriceVO.setTakeActualPrice(itemCalcDTO.getActualTotalPrice().divide(BigDecimal.valueOf(itemCalcDTO.getAmount()),2,RoundingMode.HALF_UP));
             takeActualPriceVO.setQuantity(itemCalcDTO.getAmount());
             takeActualPriceVO.setPrice(itemCalcDTO.getPrice());
             takeActualPriceVO.setMinLadderPrice(placeOrderVO.getMinLadderPrice());
             if (!CollectionUtils.isEmpty(itemCalcDTO.getSkuPreferentialVOS())){
                 //sku到手价优惠价格
                 PricePreferentialVO skuPreferentialPrice = new PricePreferentialVO();
                 BigDecimal priceTotalAmount = BigDecimal.ZERO;
                 List<SkuPreferentialVO> priceSkuList = new ArrayList<>();
                 //sku到手价优惠活动
                 PricePreferentialVO skuPreferentialActive = new PricePreferentialVO();
                 BigDecimal activeTotalAmount = BigDecimal.ZERO;
                 List<SkuPreferentialVO> activeSkuList = new ArrayList<>();
                 //sku到手价优惠卡券
                PricePreferentialVO skuPreferentialCoupon = new PricePreferentialVO();
                 BigDecimal couponTotalAmount = BigDecimal.ZERO;
                 List<SkuPreferentialVO> couponSkuList = new ArrayList<>();
                 for (SkuPreferentialVO skuPreferentialVO : itemCalcDTO.getSkuPreferentialVOS()){
                     //优惠价格
                    if (skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.ACTIVITY.ordinal()) || skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.LADDER.ordinal())
                    || skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.LIVE_BROADCAST.ordinal()) || skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.MALL_AND_LIVE.ordinal())){
                        priceTotalAmount = priceTotalAmount.add(skuPreferentialVO.getAmount().divide(BigDecimal.valueOf(itemCalcDTO.getAmount()),2,RoundingMode.HALF_UP));
                        priceSkuList.add(skuPreferentialVO);
                    }
                    //优惠活动
                     if (skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.FULL_REDUCE.ordinal())){
                         activeTotalAmount = activeTotalAmount.add(skuPreferentialVO.getAmount().divide(BigDecimal.valueOf(itemCalcDTO.getAmount()),2,RoundingMode.HALF_UP));
                         activeSkuList.add(skuPreferentialVO);
                     }
                     //优惠卡券
                     if (skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.DISCOUNT_CARD.ordinal()) || skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.COUPON.ordinal())
                     || skuPreferentialVO.getType().equals(OrderPreferentialTypeEnum.RED_PACKET.ordinal()) || skuPreferentialVO.getType().equals(18)){
                         couponTotalAmount = couponTotalAmount.add(skuPreferentialVO.getAmount().divide(BigDecimal.valueOf(itemCalcDTO.getAmount()),2,RoundingMode.HALF_UP));
                         couponSkuList.add(skuPreferentialVO);
                     }
                 }
                 if (!CollectionUtils.isEmpty(priceSkuList)){
                     skuPreferentialPrice.setTotalAmount(priceTotalAmount);
                     skuPreferentialPrice.setSkuPreferentialList(priceSkuList);
                     takeActualPriceVO.setSkuPreferentialPrice(skuPreferentialPrice);
                 }

                 if (!CollectionUtils.isEmpty(activeSkuList)){
                     skuPreferentialActive.setTotalAmount(activeTotalAmount);
                     skuPreferentialActive.setSkuPreferentialList(activeSkuList);
                     takeActualPriceVO.setSkuPreferentialActive(skuPreferentialActive);
                 }

                 if (!CollectionUtils.isEmpty(couponSkuList)){
                     skuPreferentialCoupon.setTotalAmount(couponTotalAmount);
                     skuPreferentialCoupon.setSkuPreferentialList(couponSkuList);
                     takeActualPriceVO.setSkuPreferentialCoupon(skuPreferentialCoupon);
                 }

             }
             takeActualPriceVO.setEffectiveNum(placeOrderVO.getEffectiveNum());
             takeActualPriceVO.setCouponId(placeOrderVO.getCouponId());
             if (Objects.nonNull(placeOrderVO.getCouponSenderId())) {
                 takeActualPriceVO.setCouponSenderId(placeOrderVO.getCouponSenderId());
             }
             takeActualPriceVOS.add(takeActualPriceVO);
         }
    }

    /**
     * 构建计算到手价
     *
     * @param itemCalcDTOList sku详情
     * @param placeOrderVO    计算数据
     * @return OrderResultVO
     */
    private PlaceOrderCalcDTO takePriceHandler(List<OrderItemCalcDTO> itemCalcDTOList, PlaceOrderVO placeOrderVO) {
        log.info("构建计算到手价takePriceHandler:itemCalcDTOList:{}", JSON.toJSONString(itemCalcDTOList));
        List<AbstractOrderItemHandler> itemHandlerList;
        List<AbstractPlaceOrderHandler> orderHandlerList;
        itemHandlerList = OrderCalcFollowConfig.TAKE_ACTUAL_PRICE_ITEM_HANDLER;
        orderHandlerList = OrderCalcFollowConfig.TAKE_ACTUAL_PRICE_ORDER_HANDLER;

        OrderResultVO resultVO = new OrderResultVO();
        for (OrderItemCalcDTO dto : itemCalcDTOList) {
            for (AbstractOrderItemHandler itemHandler : itemHandlerList) {
                boolean oiJointCalc = itemHandler.handleOrderItem(dto, placeOrderVO, resultVO);
                //当前优惠计算过，不再计算后续优惠
                if (oiJointCalc) {
                    log.info("sku项计算到手价：{}sku项金额计算，sku：{}使用了{}，总原价：{}，使用后价格：{}",
                            placeOrderVO.getMId(), dto.getSku(), itemHandler.getClass().getSimpleName(), dto.getActualTotalOriginalPrice(), dto.getActualTotalPrice());
                    break;
                }
            }
        }
        PlaceOrderCalcDTO orderCalcDTO = new PlaceOrderCalcDTO();
        orderCalcDTO.setPlaceOrderVO(placeOrderVO);
        orderCalcDTO.setItemCalcDTOList(itemCalcDTOList);
        //非冲突计算到手价
        StringBuilder sb = new StringBuilder("非冲突计算到手价");
        for (AbstractPlaceOrderHandler orderHandler : orderHandlerList) {
            boolean orderJointCalc = orderHandler.handlePlaceOrder(orderCalcDTO, resultVO);
            sb.append("，").append(orderHandler.getClass().getSimpleName()).append("：").append(orderJointCalc);
        }
        log.info(sb.toString());

        return orderCalcDTO;
    }


    /**
     * 到手价计算item构造
     *
     * @param trolleyList 到手价list
     * @param placeOrderVO 计算VO
     * @param controlProductsMap
     * @return 到手价计算item
     */
    private List<OrderItemCalcDTO> orderItemCacheBuild(List<Trolley> trolleyList, PlaceOrderVO placeOrderVO,
                                                       Map<String, MarketPriceControlProducts> controlProductsMap) {
        Integer areaNo = placeOrderVO.getArea().getAreaNo();
        Integer largeAreaNo = placeOrderVO.getArea().getLargeAreaNo();
        List<OrderItemCalcDTO> result = new ArrayList<>();
        List<String> skuList = trolleyList.stream().map(Trolley::getSku).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuList)) {
            return result;
        }

        //计算搭配购是否可用 -- 已废弃
        /*String collocationDays = configService.getValue(ConfigKey.COLLOCATION_FIRST_PREFERENTIAL_DAYS);
        Map<String, Boolean> collocationUsableMap = collocationService.collocationUsable(placeOrderVO.getMId(), placeOrderVO.getArea().getAreaNo(), skuList,collocationDays);
        placeOrderVO.setCollocationUsableMap(collocationUsableMap);*/

        placeOrderVO.setAreaSkuMap(Maps.newHashMap());

        List<AreaSku> areaSkus = areaSkuMapper.selectAreaSkuByAreaNoAndSkus(areaNo, skuList);
        if (CollectionUtils.isEmpty(areaSkus)) {
            log.info("OrderCalcService[]orderItemCacheBuild[]skuVOList is empty!placeOrderVO:{}", JSON.toJSONString(placeOrderVO));
            return result;
        }
        Map<String, AreaSku> areaSkuMap = areaSkus.stream().collect(Collectors.toMap(AreaSku::getSku, Function.identity(), (a, b) -> a));
        placeOrderVO.setAreaSkuMap(areaSkuMap);

        //不是大客户
        if (!placeOrderVO.getMajorMerchant()) { //NOSONAR
            List<String> filterSkus = areaSkus.stream().filter(AreaSku::getOnSale).map(AreaSku::getSku).collect(Collectors.toList());

            //组装获取活动信息
            List<ActivitySkuDTO> activitySkuDTOS = new ArrayList<>(filterSkus.size());
            Map<String, Trolley> trolleyMap = trolleyList.stream().collect(Collectors.toMap(x -> x.getSku(), Function.identity(), (a, b) -> b));
            filterSkus.forEach(sku -> {
                if (trolleyMap.containsKey(sku)) {
                    ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
                    activitySkuDTO.setSku(sku);
                    activitySkuDTO.setQuantity(trolleyMap.get(sku).getQuantity());
                    activitySkuDTOS.add(activitySkuDTO);
                }
            });
            Map<String, ActivitySkuDetailDTO> setActivitySkuDetailMap = activityService.listCacheByActivitySku(activitySkuDTOS, areaNo, placeOrderVO.getMId(), Boolean.TRUE);
            placeOrderVO.setActivitySkuDetailMap(setActivitySkuDetailMap);
        }

        //计算获取搭配购 -- 已废弃
        /*List<SkuDiscount> skuDiscounts = skuDiscountMapper.selectBySkus(skuList);
        if (!CollectionUtils.isEmpty(skuDiscounts)){
            Map<String,SkuDiscount> skuDiscountMap = skuDiscounts.stream().collect(Collectors.toMap(SkuDiscount::getSku, Function.identity()));
            placeOrderVO.setSkuDiscountMap(skuDiscountMap);
        }*/

        Map<String, Inventory> inventoryMap = inventoryService.selectInventoryWithCacheBatch(new HashSet<>(skuList));
        Map<Long, Products> productsMap = productService.selectProductsWithCachePipeline(inventoryMap.values().stream().map(Inventory::getPdId).collect(Collectors.toSet()));

        //获取当前核心商品低价信息
        Map<String, BigDecimal> coreProductBasePriceMap = productService.getCoreProductFloorPriceMapWithCache(largeAreaNo);

        Map<Integer, Category> categoryMap = categoryService.getAllCategoryByCache();

        Map<String, ActivitySkuDetailDTO> activitySkuDetailMap = placeOrderVO.getActivitySkuDetailMap();
        for (Trolley trolley : trolleyList) {
            AreaSku areaSku = areaSkuMap.get(trolley.getSku());
            if (areaSku == null){
                log.info("OrderCalcService[]orderItemCacheBuild[]areaSku is null!sku:{}", trolley.getSku());
                continue;
            }

            Inventory inventory = inventoryMap.get(trolley.getSku());
            if (inventory == null){
                log.info("OrderCalcService[]orderItemCacheBuild[]inventory is null!sku:{}", trolley.getSku());
                continue;
            }

            Products products = productsMap.get(inventory.getPdId());
            if (products == null){
                log.info("OrderCalcService[]orderItemCacheBuild[]products is null!pId:{}", inventory.getPdId());
                continue;
            }

            Category category = categoryMap.get(products.getCategoryId());
            if (category == null){
                log.info("OrderCalcService[]orderItemCacheBuild[]category is null!placeOrderVO:{}", JSON.toJSONString(placeOrderVO));
                continue;
            }

            //假如隐藏实付价则不返回预估到手价  假如是隐藏面价需要判断是否超过控价线
            if (checkControlProducts(placeOrderVO, trolley.getSku(), controlProductsMap)) {
                continue;
            }

            //商品原价处理：大客户=定价+返点；单店=定价；
            BigDecimal originalPrice = null;
            String ladderPrice = null;
            Integer prepayUsableQuantity = null;
            BigDecimal prepaySetPrice = null;
            MajorRebate usableRebate = null;
            BigDecimal activityPrice = null;
            //大客户价格处理
            if (placeOrderVO.getMajorMerchant()) { //NOSONAR
                usableRebate = majorPriceService.queryUsableRebate(placeOrderVO.getAdminId(), placeOrderVO.getArea().getAreaNo(), trolley.getSku(), products.getCategoryId());
                originalPrice = majorPriceService.queryPriceDealWithRebate(placeOrderVO, products.getCategoryId(), trolley.getSku(), trolley.getQuantity(), usableRebate);

                //预付商品
                prepayUsableQuantity = prepayInventoryService.skuUsableQuantity(placeOrderVO.getAdminId(), placeOrderVO.getDirect(), trolley.getSku());
                if (prepayUsableQuantity != null){
                    MajorPrice majorPrice = majorPriceMapper.selectMajorPrice(placeOrderVO.getAdminId(), placeOrderVO.getDirect(), placeOrderVO.getArea().getAreaNo(), trolley.getSku());
                    //全量客户无报价单查询商城价
                    if (majorPrice != null) {
                        //商城价
                        // 这里没有处理 按商城价上下浮， 是因为 大客户没有到手价 上层不会掉到这里。
                        if (Objects.equals(0, majorPrice.getPriceType())) {
                            prepaySetPrice = areaSkuService.selectValidPrice(placeOrderVO.getArea().getAreaNo(), trolley.getSku());
                        } else {
                            prepaySetPrice = majorPrice.getPrice();
                        }
                    } else {
                        prepaySetPrice = areaSkuService.selectValidPrice(placeOrderVO.getArea().getAreaNo(), trolley.getSku());
                    }
                }
            } else if (areaSku != null && areaSku.getOnSale()) {//NOSONAR
                ActivitySkuDetailDTO skuDetailDTO = activitySkuDetailMap.get(areaSku.getSku());
                originalPrice = areaSku.getPrice();
                if (skuDetailDTO == null) {
                    ladderPrice = areaSku.getLadderPrice();
                } else {
                    activityPrice = skuDetailDTO.getActivityPrice();
                }
            }

            if (originalPrice == null) {
                throw new BizException( "哎呀，很抱歉，您选购的" + products.getPdName() + "失效了");
            }

            //价格信息，计算前原价总价、实付总价、免邮门槛总价相同
            OrderItemCalcDTO dto = new OrderItemCalcDTO();
            dto.setOriginalPrice(originalPrice);
            dto.setPrice(originalPrice);
            dto.setLadderPrice(ladderPrice);
            BigDecimal totalPrice = originalPrice.multiply(BigDecimal.valueOf(trolley.getQuantity()));
            dto.setActualTotalPrice(totalPrice);
            dto.setCalcPartDeliveryFee(totalPrice);
            dto.setActualTotalOriginalPrice(totalPrice);
            dto.setPrepayUsableQuantity(prepayUsableQuantity);
            dto.setPrepaySetPrice(prepaySetPrice);
            dto.setActivityPrice(activityPrice);

            //返点信息
            if (usableRebate != null) {
                dto.setRebateType(usableRebate.getType());
                dto.setRebateNumber(usableRebate.getNumber());
            }

            //组合包信息
            dto.setSuitId(trolley.getSuitId());

            //商品信息
            dto.setPdName(products.getPdName());
            dto.setSkuName(inventory.getSkuName());
            dto.setWeight(inventory.getWeight());
            dto.setBaseSaleUnit(inventory.getBaseSaleUnit());
            dto.setBaseSaleQuantity(inventory.getBaseSaleQuantity());
            dto.setMaturity(inventory.getMaturity());
            dto.setSkuType(inventory.getType());
            dto.setCategoryId(products.getCategoryId());
            dto.setPicturePath(Optional.ofNullable(inventory.getSkuPic()).orElse(products.getPicturePath()));
            dto.setStorageLocation(products.getStorageLocation());
            dto.setVolume(inventory.getVolume());
            dto.setWeightNum(inventory.getWeightNum());
            dto.setPdId(inventory.getPdId());
            dto.setInvId(inventory.getInvId());

            //类目信息
            dto.setCategoryType(category.getType());

            //购物车信息
            dto.setSku(trolley.getSku());
            dto.setParentSku(trolley.getParentSku());
            dto.setProductType(trolley.getProductType());
            dto.setAmount(trolley.getQuantity());
            dto.setAddTime(new Date());

            //代仓支付方式
            if (placeOrderVO.getMajorMerchant() && Objects.equals(SkuTypeEnum.AGENT.ordinal(), inventory.getType())) {//NOSONAR
                MajorPrice majorPrice = majorPriceMapper.selectMajorPrice(placeOrderVO.getAdminId(), placeOrderVO.getDirect(), areaNo, dto.getSku());
                dto.setAgentPayMethod(majorPrice != null ? majorPrice.getPayMethod() : 0);
            }

            //核心品低价
            if (!CollectionUtils.isEmpty(coreProductBasePriceMap) && coreProductBasePriceMap.containsKey(trolley.getSku())) {
                dto.setFloorPrice(coreProductBasePriceMap.get(trolley.getSku()));
            }

            result.add(dto);
        }

        //当前sku存在活动多个阶梯价并开启了获取最低阶梯价格
        if (Objects.equals(placeOrderVO.getMinLadderPrice(), CommonStatus.YES.getCode()) && placeOrderVO.getTakePriceFlag() //NOSONAR
                && !CollectionUtils.isEmpty(result)) {
            return constructMultipleSku(result, activitySkuDetailMap, placeOrderVO);
        }
        return result;
    }

    /**
     * 当前sku存在活动多个阶梯价并开启了获取最低阶梯价格
     * @param result  sku信息
     * @param activitySkuDetailMap 活动sku信息
     * @param placeOrderVO
     */
    private List<OrderItemCalcDTO> constructMultipleSku(List<OrderItemCalcDTO> result, Map<String, ActivitySkuDetailDTO> activitySkuDetailMap,
                                                        PlaceOrderVO placeOrderVO) {
        List<OrderItemCalcDTO> resultList = new ArrayList<>();
        for (OrderItemCalcDTO orderItemCalcDTO : result) {
            ActivitySkuDetailDTO activitySkuDetailDTO = activitySkuDetailMap.get(orderItemCalcDTO.getSku());
            Integer minSaleQuantity = orderItemCalcDTO.getAmount();

            //无活动信息 或活动价为空（最小起售量活动价都为空则其他阶梯不考虑） 或阶梯价为空 或阶梯价数量小等于1
            if (activitySkuDetailDTO == null || CollectionUtils.isEmpty(activitySkuDetailDTO.getLadderPrices())) {
                log.info("OrderCalcService[]constructMultipleSku[]fail[]sku:{},activitySkuDetailDTO:{}", orderItemCalcDTO.getSku(), JSON.toJSONString(activitySkuDetailDTO));
                resultList.add(orderItemCalcDTO);
                continue;
            }

            //省心送需要校验活动是否支持省心送商品
            if (Objects.equals(placeOrderVO.getTimingSkuPrice(), CommonStatus.YES.getCode()) &&
                    !Objects.equals(activitySkuDetailDTO.getIsSupportTiming(), CommonStatus.YES.getCode())) {
                log.info("OrderCalcService[]constructMultipleSku[]fail[]sku:{},activitySkuDetailDTO:{}", orderItemCalcDTO.getSku(), JSON.toJSONString(activitySkuDetailDTO));
                resultList.add(orderItemCalcDTO);
                continue;
            }

            //获取当前阶梯价中最大的阶梯数量和当前最小起售量进行对比 假如小于最小起售量 直接跳过下面逻辑
            List<LadderPriceVO> ladderPrices = activitySkuDetailDTO.getLadderPrices().stream().filter(ladderPriceVO ->
                    ladderPriceVO.getUnit().compareTo(minSaleQuantity) >= 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ladderPrices)) {
                log.info("OrderCalcService[]constructMultipleSku[]ladderPrices is empty! orderItemCalcDTO:{},activitySkuDetailDTO:{}",
                        JSON.toJSONString(orderItemCalcDTO), JSON.toJSONString(activitySkuDetailDTO));
                resultList.add(orderItemCalcDTO);
                continue;
            }

            //组装每个阶梯的信息 假如低于等于最低起售量则按照最低起售量计算
            Set<Integer> amountList = new HashSet<>();
            activitySkuDetailDTO.getLadderPrices().forEach(ladderPriceVO -> {
                if (orderItemCalcDTO.getAmount() >= ladderPriceVO.getUnit()) {
                    if (amountList.contains(orderItemCalcDTO.getAmount())) {
                        return;
                    }
                    resultList.add(orderItemCalcDTO);
                    amountList.add(orderItemCalcDTO.getAmount());
                    return;
                }
                OrderItemCalcDTO calcDTO = OrderItemConverter.toItemDTO(orderItemCalcDTO, ladderPriceVO);

                //人群包价格是实时计算出来
                if (Objects.equals(activitySkuDetailDTO.getIsCrowdPack(), CommonStatus.YES.getCode())) {
                    //需要单独计算活动价格
                    PriceStrategy priceStrategy = new PriceStrategy();
                    priceStrategy.setAmount(ladderPriceVO.getAmount());
                    priceStrategy.setAdjustType(ladderPriceVO.getAdjustType());
                    priceStrategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                    BigDecimal costPrice = null;
                    if (Objects.equals(InventoryAdjustPriceTypeEnum.GROSS_PROFIT_PERCENTAGE.getType(), priceStrategy.getAdjustType())) {
                        costPrice = inventoryService.selectCostPriceByCache(orderItemCalcDTO.getSku(), RequestHolder.getMerchantAreaNo());
                    }
                    BigDecimal activityPrice = priceStrategyService.calcStrategyPrice(priceStrategy, costPrice, calcDTO.getPrice());
                    calcDTO.setActivityPrice(activityPrice);
                }
                resultList.add(calcDTO);
            });

            //获取当前阶梯价中最小的阶梯数量和当前最小起售量进行对比 假如大于最小起售量 也需要用最小起售量参与计算
            LadderPriceVO minLadderPriceVO = activitySkuDetailDTO.getLadderPrices().stream().sorted(Comparator.comparing(LadderPriceVO::getUnit)).findFirst().get();
            if (minLadderPriceVO != null  && minSaleQuantity < minLadderPriceVO.getUnit() && !amountList.contains(orderItemCalcDTO.getAmount())) {
                log.info("OrderCalcService[]constructMultipleSku[]min quantity[]sku:{},minSaleQuantity:{},minLadderPriceVO:{}", orderItemCalcDTO.getSku(),
                        minSaleQuantity, JSON.toJSONString(minLadderPriceVO));
                resultList.add(orderItemCalcDTO);
            }
        }
        log.info("OrderCalcService[]constructMultipleSku[]end[]result:{}", JSON.toJSONString(resultList));
        return resultList;
    }

    /**
     * 过滤控价品 -- 假如隐藏实付价则不返回预估到手价  假如是隐藏面价需要判断是否超过控价线
     * @param placeOrderVO
     * @param sku
     */
    private boolean checkControlProducts(PlaceOrderVO placeOrderVO, String sku, Map<String, MarketPriceControlProducts> controlProductsMap) {
        if (!placeOrderVO.getTakePriceFlag()) {//NOSONAR
            return false;
        }
        //假如隐藏实付价则不返回预估到手价  假如是隐藏面价需要判断是否超过控价线
        if (CollectionUtils.isEmpty(controlProductsMap) || !controlProductsMap.containsKey(sku)) {
            return false;
        }

        MarketPriceControlProducts controlProducts = controlProductsMap.get(sku);
        if (controlProducts != null){
            if (Objects.equals(controlProducts.getPriceHide(), MarketControlPriceHideEnum.HIDE.getCode())
                    || (Objects.equals(controlProducts.getFacePriceHide(), MarketControlPriceHideEnum.HIDE.getCode())
                    && (controlProducts.getPriceControlLine() == null || controlProducts.getPriceControlLine().compareTo(new BigDecimal(BigInteger.ZERO)) <= 0))) {
                log.info("过滤控价品->mid:{},sku:{},controlProducts:{}", placeOrderVO.getMId(), sku, JSON.toJSONString(controlProducts));
                return true;
            }
        }
        return false;
    }

    /**
     * 存在多个阶梯的情况下 选出最优的价格信息并校验控价线逻辑
     * @param takeActualPriceVOS
     * @param placeOrderVO
     */
    private List<TakeActualPriceVO> chooseBestPrice(List<TakeActualPriceVO> takeActualPriceVOS, PlaceOrderVO placeOrderVO,
                                                    Map<String, MarketPriceControlProducts> controlProductsMap) {
        //存在多个阶梯的情况下 选出最优的阶梯价格
        if (Objects.equals(placeOrderVO.getMinLadderPrice(), CommonStatus.YES.getCode()) && placeOrderVO.getTakePriceFlag()//NOSONAR
                && !CollectionUtils.isEmpty(takeActualPriceVOS)) {//NOSONAR
            log.info("OrderCalcService[]chooseBestPrice[]start[]takeActualPriceVOS:{}", JSON.toJSONString(takeActualPriceVOS));
            Map<String, List<TakeActualPriceVO>> takeActualPriceMap = takeActualPriceVOS.stream().collect(Collectors.groupingBy(TakeActualPriceVO::getSku));
            takeActualPriceVOS = new ArrayList<>();
            for (String sku : takeActualPriceMap.keySet()) {
                List<TakeActualPriceVO> actualPriceVOS = takeActualPriceMap.get(sku);
                if (CollectionUtils.isEmpty(actualPriceVOS)) {
                    continue;
                }
                if (actualPriceVOS.size() == 1) {
                    takeActualPriceVOS.add(actualPriceVOS.get(0));
                } else {
                    //组装一个sku里面多个阶梯的预估到手价信息
                    List<TakeActualLadderPriceVO> ladderPriceVOList = new ArrayList<>();

                    //Min{（最高阶梯数量*最高阶梯对应价格-最优 优惠券金额）/最高阶梯数量）,（最低阶梯数量*最低阶梯对应价格-最优 优惠券金额）/最低阶梯数量）}
                    //若刚好相等，则取最高阶梯数量及价格
                    TakeActualPriceVO actualPriceVO = actualPriceVOS.stream().sorted(Comparator.comparing(TakeActualPriceVO::getTakeActualPrice)
                            .thenComparing(TakeActualPriceVO::getQuantity, Comparator.reverseOrder())).findFirst().get();
                    actualPriceVOS.forEach(e -> {
                        if (e.getTakeActualPrice().compareTo(actualPriceVO.getTakeActualPrice()) >= 0) {
                            TakeActualLadderPriceVO takeActualLadderPriceVO = new TakeActualLadderPriceVO();
                            takeActualLadderPriceVO.setTakeActualPrice(e.getTakeActualPrice());
                            takeActualLadderPriceVO.setQuantity(e.getQuantity());
                            ladderPriceVOList.add(takeActualLadderPriceVO);
                        }
                    });
                    actualPriceVO.setLadderPriceVOList(ladderPriceVOList);
                    takeActualPriceVOS.add(actualPriceVO);
                }
            }
        }

        //过滤低于控价线的预估到手价信息
        if (placeOrderVO.getTakePriceFlag() && !CollectionUtils.isEmpty(takeActualPriceVOS)) {//NOSONAR
            Iterator<TakeActualPriceVO> priceVOIterator = takeActualPriceVOS.iterator();
            while (priceVOIterator.hasNext()) {
                TakeActualPriceVO takeActualPriceVO = priceVOIterator.next();
                if (CollectionUtils.isEmpty(controlProductsMap) || !controlProductsMap.containsKey(takeActualPriceVO.getSku())) {
                    continue;
                }

                MarketPriceControlProducts controlProducts = controlProductsMap.get(takeActualPriceVO.getSku());
                if (Objects.equals(controlProducts.getFacePriceHide(), MarketControlPriceHideEnum.HIDE.getCode()) && controlProducts.getPriceControlLine() != null &&
                        takeActualPriceVO.getTakeActualPrice().compareTo(controlProducts.getPriceControlLine()) < 0) {
                    log.info("过滤低于控价线的预估到手价信息，takeActualPriceVO:{}, controlProducts:{}", JSON.toJSONString(takeActualPriceVO), JSON.toJSONString(controlProducts));
                    priceVOIterator.remove();
                }
            }
        }
        log.info("到手价计算结果->>>>>>>mid{},takePriceFlag:{},结果集:{}",placeOrderVO.getMId(),placeOrderVO.getTakePriceFlag(),JSON.toJSONString(takeActualPriceVOS));
        return takeActualPriceVOS;
    }

    @Override
    public List<TakeActualPriceVO> calculateDiscountByRpc(Long mId, String sku, Integer quantity) {
        // TODO: 实现RPC接口的优惠计算逻辑
        return new ArrayList<>();
    }

    /**
     * 计算组合购优惠（集成到到手价计算中）
     * @param itemCalcDTOList 订单项列表
     * @param placeOrderVO 下单信息
     * @return 组合购计算结果
     */
    public CombinationCalculateRespDTO calculateCombinationBuyDiscount(List<OrderItemCalcDTO> itemCalcDTOList, PlaceOrderVO placeOrderVO) {
        if (CollectionUtils.isEmpty(itemCalcDTOList) || placeOrderVO == null) {
            return new CombinationCalculateRespDTO();
        }

        try {
            // 构建组合购计算请求
            CombinationCalculateReqDTO request = new CombinationCalculateReqDTO();
            request.setMId(placeOrderVO.getMId());
            request.setAreaNo(placeOrderVO.getArea() != null ? placeOrderVO.getArea().getAreaNo() : null);
            request.setBusinessLine(placeOrderVO.getBusinessLine());
            request.setCalculateOptimal(true);
            request.setIncludeOtherDiscounts(true);

            // 转换订单项为组合购商品DTO
            List<CombinationBuySkuDTO> skuList = itemCalcDTOList.stream()
                    .map(item -> {
                        CombinationBuySkuDTO skuDTO = new CombinationBuySkuDTO();
                        skuDTO.setSku(item.getSku());
                        skuDTO.setSkuName(item.getSkuName());
                        skuDTO.setCategoryId(item.getCategoryId());
                        skuDTO.setQuantity(item.getAmount());
                        skuDTO.setPrice(item.getPrice());
                        skuDTO.setTotalPrice(item.getActualTotalPrice());
                        skuDTO.setHasOtherDiscount(item.getActualTotalPrice().compareTo(item.getPrice().multiply(BigDecimal.valueOf(item.getAmount()))) < 0);
                        skuDTO.setDiscountedPrice(item.getActualTotalPrice());
                        return skuDTO;
                    })
                    .collect(Collectors.toList());

            request.setSkuList(skuList);

            // 调用组合购计算服务
            return combinationBuyService.calculateCombinationDiscount(request);

        } catch (Exception e) {
            log.error("计算组合购优惠异常", e);
            return new CombinationCalculateRespDTO();
        }
    }

    /**
     * 应用组合购优惠到订单项
     * @param itemCalcDTOList 订单项列表
     * @param combinationResult 组合购计算结果
     */
    public void applyCombinationBuyDiscount(List<OrderItemCalcDTO> itemCalcDTOList, CombinationCalculateRespDTO combinationResult) {
        if (CollectionUtils.isEmpty(itemCalcDTOList) || combinationResult == null || !combinationResult.getHasDiscount()) {
            return;
        }

        try {
            // 获取最优组合方案
            List<CombinationPlanDTO> optimalPlans = combinationResult.getOptimalPlans();
            if (CollectionUtils.isEmpty(optimalPlans)) {
                return;
            }

            CombinationPlanDTO bestPlan = optimalPlans.get(0);
            List<CombinationBuySkuDTO> combinationSkus = bestPlan.getCombinationSkus();

            if (CollectionUtils.isEmpty(combinationSkus)) {
                return;
            }

            // 计算组合购优惠分摊
            BigDecimal totalCombinationAmount = combinationSkus.stream()
                    .map(sku -> sku.getDiscountedPrice() != null ? sku.getDiscountedPrice() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalDiscountAmount = bestPlan.getDiscountAmount();

            // 按比例分摊优惠到各个商品
            Map<String, BigDecimal> skuDiscountMap = new HashMap<>();
            for (CombinationBuySkuDTO combinationSku : combinationSkus) {
                if (totalCombinationAmount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal skuAmount = combinationSku.getDiscountedPrice() != null ? combinationSku.getDiscountedPrice() : BigDecimal.ZERO;
                    BigDecimal skuDiscountAmount = totalDiscountAmount.multiply(skuAmount)
                            .divide(totalCombinationAmount, 2, BigDecimal.ROUND_HALF_UP);
                    skuDiscountMap.put(combinationSku.getSku(), skuDiscountAmount);
                }
            }

            // 应用优惠到订单项
            for (OrderItemCalcDTO item : itemCalcDTOList) {
                BigDecimal skuDiscount = skuDiscountMap.get(item.getSku());
                if (skuDiscount != null && skuDiscount.compareTo(BigDecimal.ZERO) > 0) {
                    // 扣减实付总价
                    item.decreaseActualTotalPrice(skuDiscount);

                    // 添加组合购优惠明细
                    if (item.getSkuPreferentialVOS() == null) {
                        item.setSkuPreferentialVOS(new ArrayList<>());
                    }

                    SkuPreferentialVO combinationPreferential = new SkuPreferentialVO();
                    combinationPreferential.setType(19); // 组合购优惠类型，需要在OrderPreferentialTypeEnum中添加
                    combinationPreferential.setAmount(skuDiscount);
                    combinationPreferential.setValue(skuDiscount);
                    combinationPreferential.setRuleLevel(BigDecimal.ONE);
                    item.getSkuPreferentialVOS().add(combinationPreferential);

                    log.info("应用组合购优惠，SKU：{}，优惠金额：{}", item.getSku(), skuDiscount);
                }
            }

        } catch (Exception e) {
            log.error("应用组合购优惠异常", e);
        }
    }
}
