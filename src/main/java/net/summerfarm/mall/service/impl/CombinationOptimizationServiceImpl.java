package net.summerfarm.mall.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.model.dto.combination.*;
import net.summerfarm.mall.service.CombinationOptimizationService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组合购优化算法服务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class CombinationOptimizationServiceImpl implements CombinationOptimizationService {

    /**
     * 性能阈值：当商品总数超过此值时使用贪心算法，否则使用精确算法
     */
    private static final int PERFORMANCE_THRESHOLD = 50;

    /**
     * 最大组合方案数量限制
     */
    private static final int MAX_COMBINATION_PLANS = 100;

    @Override
    public List<CombinationPlanDTO> calculateOptimalPlans(CombinationBuyDTO activity, List<CombinationBuySkuDTO> skuList) {
        if (activity == null || CollectionUtils.isEmpty(skuList)) {
            return new ArrayList<>();
        }

        // 按池子分组商品
        Map<String, List<CombinationBuySkuDTO>> skusByPool = groupSkusByPool(activity, skuList);
        
        // 检查是否有足够的池子参与组合
        if (skusByPool.size() < activity.getPoolCount()) {
            log.info("商品池数量不足，无法进行组合购。需要{}个池子，实际{}个池子", activity.getPoolCount(), skusByPool.size());
            return new ArrayList<>();
        }

        // 计算商品总数，决定使用哪种算法
        int totalSkuCount = skuList.size();
        
        List<CombinationPlanDTO> plans;
        if (totalSkuCount > PERFORMANCE_THRESHOLD) {
            log.info("商品数量较多({}个)，使用贪心算法计算近似最优解", totalSkuCount);
            plans = calculateGreedyOptimalPlans(activity, skusByPool);
        } else {
            log.info("商品数量较少({}个)，使用精确算法计算最优解", totalSkuCount);
            plans = calculateExactOptimalPlans(activity, skusByPool);
        }

        // 按优惠金额排序，返回最优方案
        return plans.stream()
                .sorted((p1, p2) -> {
                    if (p1.getDiscountAmount() == null) return 1;
                    if (p2.getDiscountAmount() == null) return -1;
                    return p2.getDiscountAmount().compareTo(p1.getDiscountAmount());
                })
                .limit(MAX_COMBINATION_PLANS)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, List<CombinationBuySkuDTO>> groupSkusByPool(CombinationBuyDTO activity, List<CombinationBuySkuDTO> skuList) {
        Map<String, List<CombinationBuySkuDTO>> skusByPool = new HashMap<>();
        
        if (CollectionUtils.isEmpty(activity.getPools())) {
            return skusByPool;
        }

        // 创建SKU到池子的映射
        Map<String, String> skuToPoolMap = new HashMap<>();
        for (CombinationBuyPoolDTO pool : activity.getPools()) {
            if (!CollectionUtils.isEmpty(pool.getSkuList())) {
                for (String sku : pool.getSkuList()) {
                    skuToPoolMap.put(sku, pool.getPoolCode());
                }
            }
        }

        // 将商品按池子分组
        for (CombinationBuySkuDTO skuDTO : skuList) {
            String poolCode = skuToPoolMap.get(skuDTO.getSku());
            if (poolCode != null) {
                skuDTO.setPoolCode(poolCode);
                skusByPool.computeIfAbsent(poolCode, k -> new ArrayList<>()).add(skuDTO);
            }
        }

        return skusByPool;
    }

    @Override
    public List<List<CombinationBuySkuDTO>> generateAllCombinations(Map<String, List<CombinationBuySkuDTO>> skusByPool, List<String> poolCodes) {
        List<List<CombinationBuySkuDTO>> allCombinations = new ArrayList<>();
        
        if (CollectionUtils.isEmpty(poolCodes)) {
            return allCombinations;
        }

        // 使用回溯算法生成所有组合
        List<CombinationBuySkuDTO> currentCombination = new ArrayList<>();
        generateCombinationsRecursive(skusByPool, poolCodes, 0, currentCombination, allCombinations);
        
        return allCombinations;
    }

    /**
     * 递归生成组合方案
     */
    private void generateCombinationsRecursive(Map<String, List<CombinationBuySkuDTO>> skusByPool, 
                                             List<String> poolCodes, 
                                             int poolIndex, 
                                             List<CombinationBuySkuDTO> currentCombination, 
                                             List<List<CombinationBuySkuDTO>> allCombinations) {
        
        // 达到最大组合数量限制，停止生成
        if (allCombinations.size() >= MAX_COMBINATION_PLANS) {
            return;
        }
        
        // 如果已经处理完所有池子，添加当前组合
        if (poolIndex >= poolCodes.size()) {
            allCombinations.add(new ArrayList<>(currentCombination));
            return;
        }

        String currentPoolCode = poolCodes.get(poolIndex);
        List<CombinationBuySkuDTO> poolSkus = skusByPool.get(currentPoolCode);
        
        if (CollectionUtils.isEmpty(poolSkus)) {
            return;
        }

        // 从当前池子中选择商品
        for (CombinationBuySkuDTO sku : poolSkus) {
            currentCombination.add(sku);
            generateCombinationsRecursive(skusByPool, poolCodes, poolIndex + 1, currentCombination, allCombinations);
            currentCombination.remove(currentCombination.size() - 1);
        }
    }

    @Override
    public BigDecimal calculateCombinationDiscount(CombinationBuyDTO activity, List<CombinationBuySkuDTO> combination) {
        if (activity == null || CollectionUtils.isEmpty(combination)) {
            return BigDecimal.ZERO;
        }

        // 计算组合总金额
        BigDecimal totalAmount = combination.stream()
                .map(sku -> {
                    BigDecimal price = sku.getDiscountedPrice() != null ? sku.getDiscountedPrice() : sku.getTotalPrice();
                    return price != null ? price : BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 检查是否满足最小金额门槛
        if (activity.getMinAmount() != null && totalAmount.compareTo(activity.getMinAmount()) < 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal discountAmount = BigDecimal.ZERO;

        // 根据优惠类型计算折扣
        if (activity.getDiscountType() != null) {
            if (activity.getDiscountType() == 1 && activity.getDiscountRate() != null) {
                // 折扣率
                discountAmount = totalAmount.multiply(BigDecimal.ONE.subtract(activity.getDiscountRate()));
            } else if (activity.getDiscountType() == 2 && activity.getDiscountAmount() != null) {
                // 固定金额
                discountAmount = activity.getDiscountAmount();
            }
        }

        // 检查最大优惠金额限制
        if (activity.getMaxDiscountAmount() != null && discountAmount.compareTo(activity.getMaxDiscountAmount()) > 0) {
            discountAmount = activity.getMaxDiscountAmount();
        }

        return discountAmount;
    }

    @Override
    public List<CombinationPlanDTO> calculateGreedyOptimalPlans(CombinationBuyDTO activity, Map<String, List<CombinationBuySkuDTO>> skusByPool) {
        List<CombinationPlanDTO> plans = new ArrayList<>();

        // 获取需要参与组合的池子
        List<String> poolCodes = new ArrayList<>(skusByPool.keySet());
        if (poolCodes.size() < activity.getPoolCount()) {
            return plans;
        }

        // 贪心策略：优先选择价格最高的商品进行组合
        Map<String, List<CombinationBuySkuDTO>> sortedSkusByPool = new HashMap<>();
        for (Map.Entry<String, List<CombinationBuySkuDTO>> entry : skusByPool.entrySet()) {
            List<CombinationBuySkuDTO> sortedSkus = entry.getValue().stream()
                    .sorted((s1, s2) -> {
                        BigDecimal price1 = s1.getDiscountedPrice() != null ? s1.getDiscountedPrice() : s1.getTotalPrice();
                        BigDecimal price2 = s2.getDiscountedPrice() != null ? s2.getDiscountedPrice() : s2.getTotalPrice();
                        if (price1 == null) price1 = BigDecimal.ZERO;
                        if (price2 == null) price2 = BigDecimal.ZERO;
                        return price2.compareTo(price1); // 降序排列
                    })
                    .collect(Collectors.toList());
            sortedSkusByPool.put(entry.getKey(), sortedSkus);
        }

        // 生成贪心组合方案
        int maxPlans = Math.min(10, MAX_COMBINATION_PLANS); // 贪心算法限制方案数量
        for (int i = 0; i < maxPlans; i++) {
            List<CombinationBuySkuDTO> combination = new ArrayList<>();
            boolean validCombination = true;

            for (String poolCode : poolCodes) {
                List<CombinationBuySkuDTO> poolSkus = sortedSkusByPool.get(poolCode);
                if (poolSkus.size() > i) {
                    combination.add(poolSkus.get(i));
                } else {
                    validCombination = false;
                    break;
                }
            }

            if (validCombination && isValidCombination(activity, combination)) {
                CombinationPlanDTO plan = createPlan(activity, combination, "greedy_" + i);
                if (plan.getIsValid()) {
                    plans.add(plan);
                }
            }
        }

        return plans;
    }

    @Override
    public List<CombinationPlanDTO> calculateExactOptimalPlans(CombinationBuyDTO activity, Map<String, List<CombinationBuySkuDTO>> skusByPool) {
        List<CombinationPlanDTO> plans = new ArrayList<>();

        // 获取需要参与组合的池子
        List<String> poolCodes = new ArrayList<>(skusByPool.keySet());
        if (poolCodes.size() < activity.getPoolCount()) {
            return plans;
        }

        // 生成所有可能的组合
        List<List<CombinationBuySkuDTO>> allCombinations = generateAllCombinations(skusByPool, poolCodes);

        // 计算每个组合的优惠金额
        int planIndex = 0;
        for (List<CombinationBuySkuDTO> combination : allCombinations) {
            if (isValidCombination(activity, combination)) {
                CombinationPlanDTO plan = createPlan(activity, combination, "exact_" + planIndex++);
                if (plan.getIsValid()) {
                    plans.add(plan);
                }
            }
        }

        return plans;
    }

    @Override
    public boolean isValidCombination(CombinationBuyDTO activity, List<CombinationBuySkuDTO> combination) {
        if (activity == null || CollectionUtils.isEmpty(combination)) {
            return false;
        }

        // 检查是否包含所有必需的池子
        Set<String> poolCodes = combination.stream()
                .map(CombinationBuySkuDTO::getPoolCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        return poolCodes.size() >= activity.getPoolCount();
    }

    /**
     * 创建组合方案
     */
    private CombinationPlanDTO createPlan(CombinationBuyDTO activity, List<CombinationBuySkuDTO> combination, String planId) {
        CombinationPlanDTO plan = new CombinationPlanDTO(planId, activity.getId());

        // 设置组合商品
        plan.setCombinationSkus(combination);

        // 按池子分组
        Map<String, List<CombinationBuySkuDTO>> skusByPool = combination.stream()
                .collect(Collectors.groupingBy(CombinationBuySkuDTO::getPoolCode));
        plan.setCombinationSkusByPool(skusByPool);

        // 计算原价
        BigDecimal originalAmount = combination.stream()
                .map(sku -> {
                    BigDecimal price = sku.getDiscountedPrice() != null ? sku.getDiscountedPrice() : sku.getTotalPrice();
                    return price != null ? price : BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        plan.setOriginalAmount(originalAmount);

        // 计算优惠金额
        BigDecimal discountAmount = calculateCombinationDiscount(activity, combination);
        plan.setDiscountAmount(discountAmount);

        // 计算优惠后金额
        plan.calculateDiscountedAmount();
        plan.calculateDiscountRate();
        plan.setPriorityByDiscount();

        // 设置有效性
        plan.setIsValid(discountAmount.compareTo(BigDecimal.ZERO) > 0);

        // 生成描述
        plan.setDescription(generatePlanDescription(activity, combination, discountAmount));

        return plan;
    }

    /**
     * 生成方案描述
     */
    private String generatePlanDescription(CombinationBuyDTO activity, List<CombinationBuySkuDTO> combination, BigDecimal discountAmount) {
        StringBuilder desc = new StringBuilder();
        desc.append("组合购【").append(activity.getActivityName()).append("】");

        Map<String, Long> poolCounts = combination.stream()
                .collect(Collectors.groupingBy(CombinationBuySkuDTO::getPoolCode, Collectors.counting()));

        desc.append("，包含");
        poolCounts.forEach((poolCode, count) -> {
            desc.append(poolCode).append("池").append(count).append("件，");
        });

        if (activity.getDiscountType() != null && activity.getDiscountType() == 1) {
            desc.append("享受").append(activity.getDiscountRate().multiply(BigDecimal.valueOf(10))).append("折优惠");
        } else {
            desc.append("减免").append(discountAmount).append("元");
        }

        return desc.toString();
    }
}
