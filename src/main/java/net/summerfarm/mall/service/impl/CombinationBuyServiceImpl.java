package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.model.dto.combination.*;
import net.summerfarm.mall.service.CombinationBuyService;
import net.summerfarm.mall.service.CombinationOptimizationService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组合购服务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class CombinationBuyServiceImpl implements CombinationBuyService {

    @Resource
    private CombinationOptimizationService combinationOptimizationService;

    // TODO: 注入相关的Mapper
    // @Resource
    // private CombinationBuyMapper combinationBuyMapper;
    // @Resource
    // private CombinationBuyPoolMapper combinationBuyPoolMapper;
    // @Resource
    // private CombinationBuyPoolSkuMapper combinationBuyPoolSkuMapper;
    // @Resource
    // private CombinationBuyRecordMapper combinationBuyRecordMapper;

    @Override
    public CombinationCalculateRespDTO calculateCombinationDiscount(CombinationCalculateReqDTO request) {
        log.info("开始计算组合购优惠，请求参数：{}", JSON.toJSONString(request));
        
        CombinationCalculateRespDTO response = new CombinationCalculateRespDTO();
        response.setHasDiscount(false);
        
        try {
            // 获取可用的组合购活动
            List<String> skuList = request.getSkuList().stream()
                    .map(CombinationBuySkuDTO::getSku)
                    .collect(Collectors.toList());
            
            List<CombinationBuyDTO> availableActivities = matchActivitiesBySkus(
                    skuList, request.getMId(), request.getAreaNo(), request.getBusinessLine());
            
            if (CollectionUtils.isEmpty(availableActivities)) {
                log.info("未找到可用的组合购活动，用户：{}，商品：{}", request.getMId(), skuList);
                response.setNonCombinationSkus(request.getSkuList());
                return response;
            }

            // 计算原价总金额
            BigDecimal originalTotalAmount = request.getSkuList().stream()
                    .map(sku -> sku.getTotalPrice() != null ? sku.getTotalPrice() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            response.setOriginalTotalAmount(originalTotalAmount);

            // 寻找最优活动和组合方案
            CombinationBuyDTO bestActivity = null;
            List<CombinationPlanDTO> bestPlans = null;
            BigDecimal maxDiscount = BigDecimal.ZERO;

            for (CombinationBuyDTO activity : availableActivities) {
                // 验证使用条件
                if (!validateCombinationUsage(activity, request.getMId(), originalTotalAmount)) {
                    continue;
                }

                // 计算最优组合方案
                List<CombinationPlanDTO> plans = calculateOptimalCombinations(activity, request.getSkuList());
                if (!CollectionUtils.isEmpty(plans)) {
                    BigDecimal activityMaxDiscount = plans.get(0).getDiscountAmount();
                    if (activityMaxDiscount != null && activityMaxDiscount.compareTo(maxDiscount) > 0) {
                        maxDiscount = activityMaxDiscount;
                        bestActivity = activity;
                        bestPlans = plans;
                    }
                }
            }

            if (bestActivity != null && !CollectionUtils.isEmpty(bestPlans)) {
                // 设置最优结果
                response.setHasDiscount(true);
                response.setCombinationBuyId(bestActivity.getId());
                response.setActivityName(bestActivity.getActivityName());
                response.setOptimalPlans(bestPlans);
                response.setCombinationDiscountAmount(maxDiscount);
                response.setCombinationDiscountedAmount(originalTotalAmount.subtract(maxDiscount));

                // 计算未参与组合的商品
                Set<String> combinationSkus = bestPlans.get(0).getCombinationSkus().stream()
                        .map(CombinationBuySkuDTO::getSku)
                        .collect(Collectors.toSet());
                
                List<CombinationBuySkuDTO> nonCombinationSkus = request.getSkuList().stream()
                        .filter(sku -> !combinationSkus.contains(sku.getSku()))
                        .collect(Collectors.toList());
                response.setNonCombinationSkus(nonCombinationSkus);

                // 生成优惠描述
                response.setDiscountDescription(generateDiscountDescription(bestActivity, bestPlans.get(0)));
                
                log.info("找到最优组合购方案，活动：{}，优惠金额：{}", bestActivity.getActivityName(), maxDiscount);
            } else {
                response.setNonCombinationSkus(request.getSkuList());
                log.info("未找到有效的组合购方案，用户：{}", request.getMId());
            }

            // 计算总优惠和最终金额
            response.calculateTotalDiscount();
            response.calculateFinalAmount();

        } catch (Exception e) {
            log.error("计算组合购优惠异常，请求：{}", JSON.toJSONString(request), e);
            response.setHasDiscount(false);
            response.setNonCombinationSkus(request.getSkuList());
        }

        return response;
    }

    @Override
    public List<CombinationBuyDTO> getAvailableActivities(Long mId, Integer areaNo, Integer businessLine) {
        // TODO: 从数据库查询可用活动
        // 这里需要根据用户类型、区域、业务线、时间等条件过滤
        List<CombinationBuyDTO> activities = new ArrayList<>();
        
        // 示例代码，实际需要从数据库查询
        // List<CombinationBuy> dbActivities = combinationBuyMapper.selectAvailableActivities(mId, areaNo, businessLine);
        // activities = convertToDTO(dbActivities);
        
        return activities.stream()
                .filter(activity -> activity.isValid() && !activity.isReachLimit())
                .collect(Collectors.toList());
    }

    @Override
    public List<CombinationBuyDTO> matchActivitiesBySkus(List<String> skuList, Long mId, Integer areaNo, Integer businessLine) {
        if (CollectionUtils.isEmpty(skuList)) {
            return new ArrayList<>();
        }

        // 获取所有可用活动
        List<CombinationBuyDTO> availableActivities = getAvailableActivities(mId, areaNo, businessLine);
        
        // 过滤出包含当前商品的活动
        return availableActivities.stream()
                .filter(activity -> {
                    if (CollectionUtils.isEmpty(activity.getPools())) {
                        return false;
                    }
                    
                    // 检查是否有足够的池子包含当前商品
                    Set<String> matchedPools = new HashSet<>();
                    for (CombinationBuyPoolDTO pool : activity.getPools()) {
                        if (!CollectionUtils.isEmpty(pool.getSkuList())) {
                            for (String sku : skuList) {
                                if (pool.getSkuList().contains(sku)) {
                                    matchedPools.add(pool.getPoolCode());
                                    break;
                                }
                            }
                        }
                    }
                    
                    return matchedPools.size() >= activity.getPoolCount();
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<CombinationPlanDTO> calculateOptimalCombinations(CombinationBuyDTO activity, List<CombinationBuySkuDTO> skuList) {
        if (activity == null || CollectionUtils.isEmpty(skuList)) {
            return new ArrayList<>();
        }

        return combinationOptimizationService.calculateOptimalPlans(activity, skuList);
    }

    @Override
    public boolean validateCombinationUsage(CombinationBuyDTO activity, Long mId, BigDecimal totalAmount) {
        if (activity == null || mId == null) {
            return false;
        }

        // 检查活动是否有效
        if (!activity.isValid()) {
            return false;
        }

        // 检查是否达到总使用次数限制
        if (activity.isReachLimit()) {
            return false;
        }

        // 检查用户使用次数限制
        if (activity.getUserLimitTimes() != null && activity.getUserLimitTimes() > 0) {
            int userUsageCount = getUserUsageCount(mId, activity.getId());
            if (userUsageCount >= activity.getUserLimitTimes()) {
                return false;
            }
        }

        // 检查最小金额门槛
        if (activity.getMinAmount() != null && totalAmount != null) {
            return totalAmount.compareTo(activity.getMinAmount()) >= 0;
        }

        return true;
    }

    @Override
    public void recordCombinationUsage(Long combinationBuyId, Long mId, String orderNo, CombinationPlanDTO plan) {
        try {
            // TODO: 记录使用记录到数据库
            // CombinationBuyRecord record = new CombinationBuyRecord();
            // record.setCombinationBuyId(combinationBuyId);
            // record.setMId(mId);
            // record.setOrderNo(orderNo);
            // record.setCombinationSkus(JSON.toJSONString(plan.getCombinationSkus()));
            // record.setOriginalAmount(plan.getOriginalAmount());
            // record.setDiscountAmount(plan.getDiscountAmount());
            // record.setFinalAmount(plan.getDiscountedAmount());
            // record.setUseTime(LocalDateTime.now());
            // record.setCreateTime(LocalDateTime.now());
            // combinationBuyRecordMapper.insert(record);
            
            log.info("记录组合购使用记录，活动ID：{}，用户：{}，订单：{}", combinationBuyId, mId, orderNo);
        } catch (Exception e) {
            log.error("记录组合购使用记录失败", e);
        }
    }

    @Override
    public int getUserUsageCount(Long mId, Long combinationBuyId) {
        // TODO: 从数据库查询用户使用次数
        // return combinationBuyRecordMapper.countByMIdAndCombinationBuyId(mId, combinationBuyId);
        return 0;
    }

    /**
     * 生成优惠描述
     */
    private String generateDiscountDescription(CombinationBuyDTO activity, CombinationPlanDTO plan) {
        StringBuilder desc = new StringBuilder();
        desc.append("组合购优惠【").append(activity.getActivityName()).append("】");
        
        if (activity.getDiscountType() != null && activity.getDiscountType() == 1 && activity.getDiscountRate() != null) {
            BigDecimal discountPercent = BigDecimal.ONE.subtract(activity.getDiscountRate()).multiply(BigDecimal.valueOf(100));
            desc.append("，享受").append(discountPercent.intValue()).append("%优惠");
        } else if (plan.getDiscountAmount() != null) {
            desc.append("，减免").append(plan.getDiscountAmount()).append("元");
        }
        
        return desc.toString();
    }
}
