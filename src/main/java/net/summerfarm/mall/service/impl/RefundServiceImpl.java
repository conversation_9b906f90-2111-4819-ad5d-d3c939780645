package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.enums.*;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.CouponEnum;
import net.summerfarm.mall.enums.DistOrderSourceEnum;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.OfcAfterSaleBeforeDeliveryFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreUnLockReq;
import net.summerfarm.mall.service.facade.dto.OrderUnLockSkuDetailReqDTO;
import net.summerfarm.mall.service.helper.AfterHandleTypeExecuteHelper;
import net.summerfarm.mall.service.helper.AfterSaleOrderHelper;
import net.summerfarm.mall.task.AsyncTaskService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.RoundingMode.HALF_UP;
import static net.summerfarm.enums.RefundReponseEnum.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-07-01
 * @description 退款成功后续处理
 */
@Slf4j
@Service
@Setter
public class RefundServiceImpl implements RefundService {
    public static final String REFUND_RETRY_COUNT = "refundRetryCount";
    public static final String CHECK_BILL_ROBOT_URL = "checkBillRobotUrl";
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private RefundHandleEventMapper refundHandleEventMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private OrderService orderService;
    @Resource
    @Lazy
    private AsyncTaskService asyncTaskService;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private MemberService memberService;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private PrepayInventoryService prepayInventoryService;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private PaymentHandler paymentHandler;
    @Resource
    private ConfigService configService;
    @Resource
    MerchantService merchantService;
    @Resource
    private OfcAfterSaleBeforeDeliveryFacade ofcAfterSaleBeforeDelivery;
    @Resource
    private WmsAreaStoreFacade areaStoreFacade;
    @Resource
    private OrderRelationService orderRelationService;
    @Resource
    private AfterHandleTypeExecuteHelper afterHandleTypeExecuteHelper;
    @Resource
    private OrderItemPreferentialMapper orderItemPreferentialMapper;
    @Resource
    private AfterHandleTypeExecuteHelper afterSaleOrderHandleHelper;
    @Resource
    private OrderPreferentialMapper orderPreferentialMapper;

    @Resource
    private AfterSaleOrderHelper afterSaleOrderHelper;

    @Resource
    private MasterOrderMapper masterOrderMapper;
    @Resource
    private MasterPaymentService masterPaymentService;

    private static final Logger logger = LoggerFactory.getLogger(FenceServiceImpl.class);

    /**
     * 实际退款
     */
    @Override
    public void refundReal() {
        log.info("实际退款任务执行中");
        List<RefundHandleEvent> events = refundHandleEventMapper.selectAllByStatus(Arrays.asList(HandleEventStatus.IN_HANDLING.ordinal(),
                HandleEventStatus.NEW.ordinal()));
        for (RefundHandleEvent event : events) {
            try {
                handleEvent(event);
            } catch (Exception e) {
                log.error("处理退款出错:{}", event, e);
            }
        }
    }

    private void handleEvent(RefundHandleEvent event) {
        log.info("正在处理退款事件:{}", event);
        RefundReponseEnum status = P;
        Refund refund = refundMapper.selectByRefundNo(event.getRefundNo());
        log.info("refund:{}", refund);
        String failReason = "";
        if (HandleEventStatus.NEW.ordinal() == event.getStatus()) {
            if (refund.getStatus() == RefundStatusEnum.IN_HANLING.ordinal()) {
                try {
                    status = performRefund(refund);
                    log.info("status = {}", status);
                } catch (Exception e) {
                    log.error("执行退款出错", e);
                    failReason = e.getMessage();
                    status = F;
                }
                recordRetry(event, status, refund, failReason);
            }
        } else if (HandleEventStatus.IN_HANDLING.ordinal() == event.getStatus()) {
            try {
                status = lookup(event);
            } catch (Exception e) {
                log.error("查询退款出错", e);
                status = F;
            }
            if (F == status) {
                try {
                    status = performRefund(refund);
                } catch (Exception e) {
                    status = F;
                    failReason = e.getMessage();
                    log.error("执行退款出错", e);
                }
                recordRetry(event, status, refund, failReason);
            }
        }

        if (status == S) {
            finish(refund, event);
        }
    }

    private void recordRetry(RefundHandleEvent event, RefundReponseEnum status, Refund refund, String failReason) {
        RefundHandleEvent eventUpdated = new RefundHandleEvent();
        eventUpdated.setId(event.getId());
        eventUpdated.setRetryCount(event.getRetryCount() + 1);
        refundHandleEventMapper.updateByPrimaryKeySelective(eventUpdated);
        Integer retryCount = Optional.ofNullable(configService.getValue(REFUND_RETRY_COUNT))
                .map(Integer::valueOf)
                .orElse(6);
        if (status == F && eventUpdated.getRetryCount() >= retryCount) {
            handleFail(refund, failReason);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    protected void handleFail(Refund refund, String reason) {
        refund.setStatus((byte) RefundStatusEnum.FAIL.ordinal());
        refund.setEndTime(new Date());
        refund.setErrCodeDes(reason);
        refundHandleEventMapper.updateStatusByRefundNo(HandleEventStatus.FAIL.ordinal(), refund.getRefundNo());
        refundMapper.updateByPrimaryKeySelective(refund);

        Integer retryCount = Optional.ofNullable(configService.getValue(REFUND_RETRY_COUNT))
                .map(i -> Integer.valueOf(i)).orElse(6);
        String url = Optional.ofNullable(configService.getValue(CHECK_BILL_ROBOT_URL))
                .orElse("");
        Map<String, String> md = new HashMap<>(2);
        md.put("title", "实际退款失败");
        String sb = "#### " +
                " <font color=#0089FF>@所有人</font> 实际退款失败\n" +
                "> ###### 招行退款执行" + retryCount + "次均失败,请关注!" + "" + "\n" +
                "> ###### refundNo:" + refund.getRefundNo() + ",afterSaleOrderNo:" + refund.getAfterSaleOrderNo() + ",退款金额:" + refund.getRefundFee().divide(BigDecimal.valueOf(100)).setScale(2, HALF_UP) + "\n" +
                "最新失败原因:" + reason;
        md.put("text", sb);
        if (Strings.isNullOrEmpty(url)) {
            log.error("配置错误,发送钉钉失败:{}", sb);
        }
        DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN, url, () -> md);

    }

    @Transactional(rollbackFor = Exception.class)
    protected void finish(Refund refund, RefundHandleEvent event) {
        log.info("refund_no:{},成功退款", refund.getRefundNo());
        refund.setStatus((byte) RefundStatusEnum.SUCCESS.ordinal());
        refund.setEndTime(new Date());
        refundMapper.updateByPrimaryKeySelective(refund);
        refundHandleEventMapper.deleteByPrimaryKey(event.getId());
    }

    protected RefundReponseEnum lookup(RefundHandleEvent event) {
        return getRefundReponseEnum(paymentHandler.queryRefund(event.getRefundNo()));
    }

    /**
     * 执行实际退款
     * @param refund 退款
     * @return 退款状态 已退款 失败 退款中
     */
    public RefundReponseEnum performRefund(Refund refund) {
        return getRefundReponseEnum(paymentHandler.performRefund(refund));
    }

    /**
     * 处理本地退款逻辑
     * @param refund 退款信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterRefund(Refund refund) {
        //1.更新退款表
        refund.setStatus(Integer.valueOf(RefundStatusEnum.IN_HANLING.ordinal()).byteValue());
        refundMapper.updateByPrimaryKeySelective(refund);

        //预售更新
        String orderNo = refund.getOrderNo().split("_")[0];
        Orders order = ordersMapper.selectByOrderNo(orderNo);

        String afterSaleOrderNo = refund.getAfterSaleOrderNo();
        AfterSaleOrderVO afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        String sku = afterSaleOrder.getSku();
        Integer quantity = afterSaleOrder.getQuantity();
        HashMap selectKey = new HashMap();
        selectKey.put("mId", afterSaleOrder.getmId());
        Merchant merchant = merchantMapper.selectOne(selectKey);
        Boolean bigMerchant = merchantService.checkCoreCustomers(merchant.getmId());


        BigDecimal afterSaleMoney = BigDecimal.ZERO;
        //总共付的钱-已经售后的钱
        AfterSaleOrder selectData = new AfterSaleOrder();
        selectData.setOrderNo(orderNo);
        selectData.setStatus(2);
        List<AfterSaleOrderVO> selectList = afterSaleOrderService.selectAfterSaleOrderVO(selectData);
        if (!CollectionUtils.isEmpty(selectList)) {
            for (AfterSaleOrderVO afterSaleOrderVO : selectList) {
                if (!afterSaleOrderVO.getAfterSaleOrderNo().equals(refund.getAfterSaleOrderNo())) {
                    afterSaleMoney = afterSaleMoney.add(afterSaleOrderVO.getHandleNum());
                }

            }
        }
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);
        Long contactId = CollectionUtils.isEmpty(deliveryPlanVOS) ? null : deliveryPlanVOS.get(0).getContactId();

        //6.如果是未到货且是 缺货，拍多/拍错/不想要 反库存
        if (afterSaleOrder.getDeliveryed() == 0 && ("拍多/拍错/不想要".equalsIgnoreCase(afterSaleOrder.getRefundType()) ||
                "缺货".equalsIgnoreCase(afterSaleOrder.getRefundType()))) {
            List<String> skus = new ArrayList<>();
            Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
            //如果是整单退
            if (afterSaleOrder.getType() != null && afterSaleOrder.getType() == 3) {
                logger.info("order refund dAll");
                //有组合包 一起退掉  更改退款状态
                List<OrderItem> orderItems = orderItemMapper.selectOrderItemByArea(orderNo, merchant.getAreaNo());

                //顺序处理库存、避免死锁
                orderItems.sort(Comparator.comparing(OrderItem::getSku));
                for (OrderItem orderItem : orderItems) {
                    //没有退款的
                    if (orderItem.getStatus() != 8) {
                        int total = 0;
                        AfterSaleOrder selectData2 = new AfterSaleOrder();
                        selectData2.setOrderNo(orderNo);
                        selectData2.setSuitId(orderItem.getSuitId());
                        if (orderItem.getSuitId() <= 0) {
                            selectData2.setSku(orderItem.getSku());
                            selectData.setProductType(orderItem.getProductType());
                        }
                        selectData2.setStatus(2);
                        List<AfterSaleOrderVO> selectList2 = afterSaleOrderMapper.selectSuccessByAfterSaleOrder(selectData2);
                        if (!CollectionUtils.isEmpty(selectList2)) {
                            for (AfterSaleOrderVO afterSaleOrderVO : selectList2) {
                                Inventory inventory = inventoryMapper.selectBySku(orderItem.getSku());
                                if (afterSaleOrderVO.getDeliveryed() == 0) {
                                    total = total + afterSaleOrderVO.getQuantity();
                                } else {
                                    int value = afterSaleOrderVO.getQuantity() / inventory.getAfterSaleQuantity();
                                    if (afterSaleOrderVO.getQuantity() % inventory.getAfterSaleQuantity() > 0) {
                                        value++;
                                    }
                                    total = total + value;
                                }
                            }
                        }

                        if (afterSaleOrder.getSuitId() <= 0) {
                            /*orderService.updateStock(orderItem.getSku(), merchant.getAreaNo(), orderItem.getAmount() - total
                                    , merchant.getMname(), SaleStockChangeTypeEnum.ALL_RETURN, orderNo, recordMap, bigMerchant, contactId);*/
                            //库存释放 新模型
                            AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
                            areaStoreUnLockReq.setContactId(contactId);
                            areaStoreUnLockReq.setOrderType(net.summerfarm.enums.SaleStockChangeTypeEnum.CANCEL_ORDER.getTypeName());
                            areaStoreUnLockReq.setOrderNo(orderNo);
                            areaStoreUnLockReq.setIdempotentNo(afterSaleOrderNo);
                            areaStoreUnLockReq.setOperatorNo(afterSaleOrderNo);
                            List<OrderUnLockSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS = new ArrayList<>();
                            OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                            orderUnLockSkuDetailReqDTO.setSkuCode(orderItem.getSku());
                            orderUnLockSkuDetailReqDTO.setReleaseQuantity(orderItem.getAmount() - total);
                            orderReleaseSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
                            areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOS);
                            areaStoreUnLockReq.setMerchantId(afterSaleOrder.getmId());
                            areaStoreUnLockReq.setSource(DistOrderSourceEnum.getDistOrderAfterSaleSourceByOrderType(order.getType()));
                            areaStoreUnLockReq.setOperatorName(merchant.getMname());
                            areaStoreFacade.storeUnLock(areaStoreUnLockReq);
                            skus.add(orderItem.getSku());
                        }
                    }


                }
            } else {
                //更改退款状态
                List<OrderItem> orderItems = orderItemMapper.selectOrderItemByArea(orderNo, merchant.getAreaNo());

                //顺序处理库存、避免死锁
                orderItems.sort(Comparator.comparing(OrderItem::getSku));
                for (OrderItem orderItem : orderItems) {
                    if (afterSaleOrder.getSuitId() <= 0) {
                        if (afterSaleOrder.getSku().equals(orderItem.getSku()) && afterSaleOrder.getSuitId().equals(orderItem.getSuitId())) {
                            //orderService.updateStock(orderItem.getSku(), merchant.getAreaNo(), afterSaleOrder.getQuantity(), merchant.getMname(), SaleStockChangeTypeEnum.RETURN, orderNo, recordMap, bigMerchant, contactId);
                            //库存释放 新模型
                            AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
                            areaStoreUnLockReq.setContactId(contactId);
                            areaStoreUnLockReq.setOrderType(net.summerfarm.enums.SaleStockChangeTypeEnum.AFTER_SALE_RETURN.getTypeName());
                            areaStoreUnLockReq.setOrderNo(orderNo);
                            areaStoreUnLockReq.setIdempotentNo(afterSaleOrderNo);
                            areaStoreUnLockReq.setOperatorNo(afterSaleOrderNo);
                            List<OrderUnLockSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS = new ArrayList<>();
                            OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                            orderUnLockSkuDetailReqDTO.setSkuCode(orderItem.getSku());
                            orderUnLockSkuDetailReqDTO.setReleaseQuantity(afterSaleOrder.getQuantity());
                            orderReleaseSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
                            areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOS);
                            areaStoreUnLockReq.setMerchantId(afterSaleOrder.getmId());
                            areaStoreUnLockReq.setSource(DistOrderSourceEnum.getDistOrderAfterSaleSourceByOrderType(order.getType()));
                            areaStoreUnLockReq.setOperatorName(merchant.getMname());
                            areaStoreFacade.storeUnLock(areaStoreUnLockReq);
                            skus.add(orderItem.getSku());
                            break;
                        }
                    }
                }
            }
            //quantityChangeRecordService.insert(recordMap);
            if (!CollectionUtils.isEmpty(skus)) {
                asyncTaskStock(skus);
            }
            //是否是预付商品
            List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectByOrderNoAndSku(orderNo, sku);

            if (!CollectionUtils.isEmpty(prepayInventoryRecords)) {
                Integer adminId = prepayInventoryRecords.get(0).getAdminId();
                boolean increase = prepayInventoryService.increase(adminId, sku, orderNo, quantity);
                if (!increase) {
                    throw new DefaultServiceException("返还预付商品异常");
                }

            }
        }
        //未到货售后返券
        if (Objects.equals(afterSaleOrder.getDeliveryed(), 0)) {
            //是否需要返券
            recoverCoupon(afterSaleOrder,order);
        }

        //4.在订单表中更新退款成功信息   全额退款时或者是整单退的时候
        if ((refund.getTotalFee().compareTo(refund.getRefundFee().add(afterSaleMoney.multiply(new BigDecimal(100)))) <= 0 && (afterSaleOrder.getHandleType().equals(AfterSaleHandleType.RETURN_SHIPPING.getType()) || afterSaleOrderHelper.isLastSku(afterSaleOrder))) || afterSaleOrder.getType() == 3) {
            logger.info("afterSaleMoney={},getRefundFee={},afterSaleOrderNo={},getTotalFee={} ", afterSaleMoney, refund.getRefundFee(),afterSaleOrderNo,refund.getTotalFee());
            Orders orders = new Orders();
            orders.setOrderNo(orderNo);
            orders.setStatus(Short.valueOf("8"));
            ordersMapper.updateByOrderNoSelective(orders);
            DeliveryPlan deliveryPlan = new DeliveryPlan();
            deliveryPlan.setOrderNo(orderNo);
            deliveryPlan.setStatus(8);
            deliveryPlan.setUpdateTime(LocalDateTime.now());
            deliveryPlanMapper.updateStatus(deliveryPlan);

            //更改退款状态
            List<OrderItem> orderItems = orderItemMapper.selectOrderItemByArea(orderNo, merchant.getAreaNo());
            for (OrderItem orderItem : orderItems) {
                if (orderItem.getStatus() != 8) {
                    orderItemMapper.updateStatusById(orderItem.getId(), 8);
                }
            }

            //扣除用户当月积分
             /* Orders record = ordersMapper.selectByOrderNo(orderNo);
            LocalDateTime startTime = Global.getMonthStartTime(LocalDateTime.now());
            if (record.getConfirmTime() != null) {
                if (DateUtils.date2LocalDateTime(record.getConfirmTime()).isAfter(startTime)) {
                    BigDecimal memberIntegral = merchant.getMemberIntegral().subtract(refund.getRefundFee().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_EVEN));
                    Merchant update = new Merchant();
                    update.setmId(record.getmId());
                    if (!"大客户".equals(merchant.getSize())) {
                        LocalDateTime start = Global.getMonthStartTime(LocalDateTime.now()).with(TemporalAdjusters.firstDayOfMonth()).minusDays(1);
                        LocalDateTime endTime = start.plusDays(1).with(TemporalAdjusters.lastDayOfMonth());
                        BigDecimal lastAmount = ordersMapper.selectTotalPriceByMonth(record.getmId(), start, endTime);
                        lastAmount = lastAmount == null ? BigDecimal.ZERO : lastAmount;
                        BigDecimal maxAmount = memberIntegral.compareTo(lastAmount) == 1 ? memberIntegral : lastAmount;
                        Integer newGrade = memberService.getGradeByMember(maxAmount, merchant.getAreaNo());
                        update.setGrade(newGrade);
                    }
                    update.setMemberIntegral(memberIntegral);
                    merchantMapper.updateByPrimaryKeySelective(update);
                }
            }*/
        }

        //5.如果是未到货
        if (afterSaleOrder.getDeliveryed() == 0) {

            //更改退款状态
            List<OrderItem> orderItems = orderItemMapper.selectOrderItemByArea(orderNo, merchant.getAreaNo());
            for (OrderItem orderItem : orderItems) {
                if (afterSaleOrder.getSuitId() <= 0) {
                    int total = afterSaleOrder.getQuantity();
                    AfterSaleOrder selectData2 = new AfterSaleOrder();
                    selectData2.setOrderNo(orderNo);
                    selectData2.setSuitId(orderItem.getSuitId());
                    if (orderItem.getSuitId() <= 0) {
                        selectData2.setSku(orderItem.getSku());
                        selectData2.setProductType(orderItem.getProductType());
                    }
                    selectData2.setAfterSaleOrderNo(afterSaleOrderNo);
                    List<AfterSaleOrderVO> selectList2 = afterSaleOrderMapper.selectSuccessByAfterSaleOrder(selectData2);
                    //列表里面包含处理过状态为2的东西
                    if (!CollectionUtils.isEmpty(selectList2)) {
                        for (AfterSaleOrderVO afterSaleOrderVO : selectList2) {
                            Inventory inventory = inventoryMapper.selectBySku(orderItem.getSku());
                            if (afterSaleOrderVO.getDeliveryed() == 0) {
                                total = total + afterSaleOrderVO.getQuantity();
                            } else {
                                total = total + afterSaleOrderVO.getQuantity() / inventory.getAfterSaleQuantity() + 1;
                            }
                        }
                    }

                    //计算销量
                    if (afterSaleOrder.getSku() != null){
                        if (afterSaleOrder.getSku().equals(orderItem.getSku())) {
                            orderService.reduceFruitSales(orderItem.getId(), order.getAreaNo());
                        }
                    }

                    if (afterSaleOrder.getSku() != null){
                        if (afterSaleOrder.getSku().equals(orderItem.getSku()) && afterSaleOrder.getSuitId().equals(orderItem.getSuitId()) && total == orderItem.getAmount()) {
                            if (afterSaleOrder.getProductType()== null || afterSaleOrder.getProductType().equals(orderItem.getProductType())){
                                orderItemMapper.updateStatusById(orderItem.getId(), 8);
                                break;
                            }
                        }
                    }
                }
            }
            //代表整单退,计算所有SKU销量，所有orderItem状态都修改
            if (afterSaleOrder.getSku() == null){
                for (OrderItem orderItem : orderItems) {
                    orderService.reduceFruitSales(orderItem.getId(), order.getAreaNo());
                    orderItemMapper.updateStatusById(orderItem.getId(), 8);
                }
            }
        }

        // 插入事件表 等待执行第三方退款
        RefundHandleEvent handleEvent = new RefundHandleEvent();
        handleEvent.setStatus(HandleEventStatus.NEW.ordinal());
        handleEvent.setRefundNo(refund.getRefundNo());
        handleEvent.setRetryCount(0);
        refundHandleEventMapper.insert(handleEvent);

        //预约返换合同数量

        //2.退款成功把售后的情况订单处理
        afterSaleOrderMapper.updateStatus(afterSaleOrderNo, 2);
        AfterSaleProof updateProof = new AfterSaleProof();
        updateProof.setId(afterSaleOrder.getId());
        updateProof.setStatus(2);
        afterSaleProofMapper.updateById(updateProof);
        log.info("订单" + orderNo + "退款申请成功");
    }

    /**
     * 处理返券
     *
     * @param afterSaleOrder 售后订单
     */
    private void recoverCoupon(AfterSaleOrderVO afterSaleOrder,Orders order) {

        Boolean isLastOne = false;
        boolean lastSku = false;

        String orderNo = afterSaleOrder.getOrderNo();
        Integer useCouponQuantity = 0;
        Integer quantity = 0;
        Integer afterQuantity = 0;
        Integer suitIdCouponQuantity = 0;
        List<OrderItemVO> orderItemVOS = new ArrayList<>();
        //退非组合包
        if (afterSaleOrder.getSuitId() <= 0) {
            orderItemVOS = orderItemMapper.selectOrderItemSuitId(orderNo, afterSaleOrder.getSku(), 0,afterSaleOrder.getProductType());
            quantity = afterSaleOrder.getQuantity();
            if (!CollectionUtils.isEmpty(orderItemVOS) && Objects.equals(orderItemVOS.get(0).getUseCoupon(), 1)) {
                useCouponQuantity = afterSaleOrder.getQuantity();
            }

        }
        /*
         * 商城--售后--营销券，此处需要判断是否存在主单 用来判断商品优惠券和运费券的退还逻辑
         */
        List<Orders> orders = null;
        Map<String, OrderRelation> orderNoMap = orderRelationService.queryMasterOrderNoByOrderNo(Collections.singletonList(orderNo));
        if (!CollectionUtils.isEmpty(orderNoMap)){
            orders = orderRelationService.selectOrdersByMasterOrderNo(orderNoMap.get(orderNo).getMasterOrderNo());
        }
        //未到货售后会更改订单项状态，已到货售后不会
        if (useCouponQuantity > 0) {
            //用券的sku已 未到货售后的数量 组合包
            Integer afterCouponQuantity = orderItemMapper.selectOrderItemActing(orderNo, 1);
            suitIdCouponQuantity = afterCouponQuantity == null ? 0 : suitIdCouponQuantity + afterCouponQuantity;
            //用券的sku已 未到货售后的数量 非组合包
            Integer notSuitIdCouponQuantity = afterSaleProofMapper.querySumQuantity(orderNo, 1,afterSaleOrder.getAfterSaleOrderNo());
            suitIdCouponQuantity = notSuitIdCouponQuantity == null ? suitIdCouponQuantity : suitIdCouponQuantity + notSuitIdCouponQuantity;
            //用券的sku总件数
            Integer totalCouponAmount = orderItemMapper.selectOrderItemActingUsable(afterSaleOrder.getOrderNo(), OrderItem.USE_COUPON);
            totalCouponAmount = totalCouponAmount == null ? 0 : totalCouponAmount;
            if (useCouponQuantity + suitIdCouponQuantity >= totalCouponAmount) {
                isLastOne = true;
            }
            //最后一个用券的sku的最后一项,核销商品券,或者整单退
            if (isLastOne || (afterSaleOrder.getType() != null && afterSaleOrder.getType() == 3)) {
                if (CollectionUtils.isEmpty(orderNoMap) || orders.size() == 1){
                    log.info("未到货退款-不拆单退商品优惠券，子订单信息-orderNo:{}", JSON.toJSONString(orderNo));
                    //不拆单退商品优惠券,这里做判断是处理新老订单兼容的问题
                    if (CollectionUtils.isEmpty(orderNoMap)){
                        merchantCouponMapper.updateByOrderNo(orderNo, afterSaleOrder.getmId(), CouponEnum.CouponTypeEnum.NORMAL.getCode());
                    }else {
                        merchantCouponMapper.updateByOrderNo(orderNoMap.get(orderNo).getMasterOrderNo(), afterSaleOrder.getmId(), CouponEnum.CouponTypeEnum.NORMAL.getCode());
                    }

                }else {
                    //拆单只退售后补偿券
                    log.info("未到货退款-拆单只退售后补偿券开始，子订单信息-orderNo:{}", JSON.toJSONString(orderNo));
                    List<OrderItemPreferential>  orderItemPreferentials = orderItemPreferentialMapper.queryByOrderNo(orderNo);
                    if (!CollectionUtils.isEmpty(orderItemPreferentials)){
                        orderItemPreferentials = orderItemPreferentials.stream().filter(item -> item.getType().equals(OrderPreferentialTypeEnum.COUPON.ordinal())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(orderItemPreferentials)){
                            Long relatedId = orderItemPreferentials.get(0).getRelatedId();
                            MerchantCouponVO merchantCouponVO = merchantCouponMapper.selectMerchantCouponWithoutStatus(relatedId.intValue());
                            //校验券类型为售后补偿券并且优惠金额不为空，则给用户退优惠面额的券
                            if (Objects.equals(merchantCouponVO.getGrouping(), CouponEnum.Group.AFTER_SALE.getCode()) && orderItemPreferentials.get(0).getAmount() != null) {
                                BigDecimal money = orderItemPreferentials.stream().map(OrderItemPreferential::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                //执行返券操作
                                AfterSaleOrderVO returnCouponAfterSaleOrderVO = new AfterSaleOrderVO();
                                returnCouponAfterSaleOrderVO.setmId(afterSaleOrder.getmId());
                                returnCouponAfterSaleOrderVO.setHandleNum(money);
                                afterSaleOrderHandleHelper.coupon(returnCouponAfterSaleOrderVO);
                                log.info("未到货退款-拆单只退售后补偿券成功，售后券信息-returnCouponAfterSaleOrderVO:{}", JSON.toJSONString(returnCouponAfterSaleOrderVO));
                            }
                        }
                    }
                }
            }
        }

        //已 未到货售后的数量 组合包数据从订单项中取，非组合包从 售后信息中取
        Integer suitIdQuantity = orderItemMapper.selectOrderItemActing(orderNo, null);
        afterQuantity = suitIdQuantity == null ? afterQuantity : afterQuantity + suitIdQuantity;

        Integer notSuitIdQuantity = afterSaleProofMapper.querySumQuantity(orderNo, null,afterSaleOrder.getAfterSaleOrderNo());
        afterQuantity = notSuitIdQuantity == null ? afterQuantity : afterQuantity + notSuitIdQuantity;
        //总件数
        Integer totalAmount = orderItemMapper.selectOrderItemActingUsable(afterSaleOrder.getOrderNo(), null);
        totalAmount = totalAmount == null ? 0 : totalAmount;
        if (afterQuantity + quantity >= totalAmount) {
            lastSku = true;
        }
        //订单的最后一个sku最后一项 退运费券,或者整单退，现在没有精准送券，不考虑
        if (lastSku || (afterSaleOrder.getType() != null && afterSaleOrder.getType() == 3)) {
            if (CollectionUtils.isEmpty(orderNoMap)  || orders.size() == 1){
                log.info("未到货退款-不拆单退红包和兑换券，子订单信息-orderNo:{}", JSON.toJSONString(orderNo));
                if (CollectionUtils.isEmpty(orderNoMap)){
                    //不拆单退红包
                    merchantCouponMapper.updateByOrderNo(orderNo, afterSaleOrder.getmId(), CouponEnum.CouponTypeEnum.RED_PACKET.getCode());
                    //不拆单退兑换券
                    merchantCouponMapper.updateByOrderNo(orderNo, afterSaleOrder.getmId(), CouponEnum.CouponTypeEnum.VOUCHER.getCode());
                }else {
                    //不拆单退红包
                    merchantCouponMapper.updateByOrderNo(orderNoMap.get(orderNo).getMasterOrderNo(), afterSaleOrder.getmId(), CouponEnum.CouponTypeEnum.RED_PACKET.getCode());
                    //不拆单退兑换券
                    merchantCouponMapper.updateByOrderNo(orderNoMap.get(orderNo).getMasterOrderNo(), afterSaleOrder.getmId(), CouponEnum.CouponTypeEnum.VOUCHER.getCode());
                }
            }

            //退权益卡根据子订单退对应次数
            afterHandleTypeExecuteHelper.backCard(orderNo);

            //此处判断当前订单外时候还有需要履约内容，true表示没有履约可以退运费券 pop订单也支持退运费券
            if (order.getType().equals(OrderTypeEnum.NORMAL.getId()) || order.getType().equals(OrderTypeEnum.POP.getId())){
                //判断没有履约，并且没有拆单或者拆单运费券没有拆，退运费券
                if (ofcAfterSaleBeforeDelivery.getAfterSaleBeforeDelivery(orderNo) && !deliveryCouponDisassemble(orders,orderNoMap)){
                    log.info("未到货退款-执行退运费券，子订单信息-orderNo:{}", JSON.toJSONString(orderNo));
                    if (CollectionUtils.isEmpty(orderNoMap)){
                        merchantCouponMapper.updateByOrderNo(orderNo, afterSaleOrder.getmId(), CouponEnum.CouponTypeEnum.DELIVERY.getCode());
                    }else {
                        merchantCouponMapper.updateByOrderNo(orderNoMap.get(orderNo).getMasterOrderNo(), afterSaleOrder.getmId(), CouponEnum.CouponTypeEnum.DELIVERY.getCode());
                    }
                }
            }else {
                merchantCouponMapper.updateByOrderNo(orderNo, afterSaleOrder.getmId(), CouponEnum.CouponTypeEnum.DELIVERY.getCode());
            }
        }

    }

    /**
     * 判断订单所用运费券是否有拆分
     * @param orders orders
     * @Return false 没有分摊，true 有分摊
     */
    private Boolean deliveryCouponDisassemble(List<Orders> orders,Map<String, OrderRelation> orderNoMap){
        if (CollectionUtils.isEmpty(orderNoMap) || orders.size() == 1){
            return Boolean.FALSE;
        }
        List<String> orderNoList = orders.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        List<OrderPreferential>  orderPreferentials = orderPreferentialMapper.selectPreferentialByOrderList(orderNoList);
        orderPreferentials = orderPreferentials.stream().
                filter(item -> item.getType() == OrderPreferentialTypeEnum.DELIVERY_COUPON.ordinal()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderPreferentials) && orderPreferentials.size() == 1){
            return Boolean.FALSE;
        }else {
            return Boolean.TRUE;
        }
    }



    /**
     * 处理到货提醒
     *
     * @param skus skus
     */
    private void asyncTaskStock(List<String> skus) {

        PurchasesConfig purchasesConfig = new PurchasesConfig();
        purchasesConfig.setAreaNo(RequestHolder.getMerchantArea().getAreaNo());
        purchasesConfig.setSkus(skus);
        asyncTaskService.purchasesArrival(purchasesConfig);
    }

    @Override
    public void notifySuccess(Refund refund) {
        Refund update = new Refund();
        update.setRefundId(refund.getRefundId());
        update.setEndTime(new Date());
        update.setStatus(refund.getStatus());
        update.setOnlineRefundEndTime(refund.getOnlineRefundEndTime());
        update.setTransactionNumber(refund.getTransactionNumber());
        refundHandleEventMapper.updateStatusByRefundNo(HandleEventStatus.SUCCESS.ordinal(), refund.getRefundNo());
        refundMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void laborRefund(String orderNo) {
        OrderVO orderVO = ordersMapper.selectByOrderyNo(orderNo);
        if (orderVO.getStatus() != OrderStatusEnum.CANCEL.getId()){
            log.info("非撤销订单不支持退款：{}",orderNo);
            return;
        }
        BigDecimal totalFen = orderVO.getTotalPrice().multiply(BigDecimal.valueOf(100));
        BigDecimal refundFen = orderVO.getTotalPrice().multiply(BigDecimal.valueOf(100));
        logger.info("refundFen={}",refundFen);
        // List<Refund> refunds = refundMapper.selectByOrderNo(orderNo);
        String refundNo = Global.createAfterSaleOrderNo(orderNo); //  orderNo + (refunds.size() + 1);
        Refund refund = new Refund(orderNo, null, refundNo, totalFen, refundFen);
        refund.setStatus(Integer.valueOf(RefundStatusEnum.IN_HANLING.ordinal()).byteValue());
        refund.setCashFee(orderVO.getTotalPrice());
        refund.setCashRefundFee(orderVO.getTotalPrice());
        refundMapper.insertSelective(refund);
        // 插入事件表 等待执行第三方退款
        RefundHandleEvent handleEvent = new RefundHandleEvent();
        handleEvent.setStatus(HandleEventStatus.NEW.ordinal());
        handleEvent.setRefundNo(refund.getRefundNo());
        handleEvent.setRetryCount(0);
        refundHandleEventMapper.insert(handleEvent);
        //查询主订单 更新主支付单支付金额 退款需要
        Map<String, OrderRelation> relationMap = orderRelationService.queryMasterOrderNoByOrderNo(Collections.singletonList(orderNo));
        if (!CollectionUtils.isEmpty(relationMap)){
            MasterPayment masterPayment = masterPaymentService.selectByMasterPaymentNo(relationMap.get(orderNo).getMasterOrderNo());
            if (masterPayment.getMoney() == null){
                MasterOrder masterOrder = masterOrderMapper.selectByMasterOrderNo(relationMap.get(orderNo).getMasterOrderNo());
                log.info("更新主支付单金额:{},{}",relationMap.get(orderNo).getMasterOrderNo(),masterOrder.getTotalPrice());
                masterPaymentService.updateMoneyByMasterPaymentNo(masterOrder.getMasterOrderNo(),masterOrder.getTotalPrice());
            }
        }

    }

    public static RefundReponseEnum getRefundReponseEnum(AjaxResult result) {
        if (result == null || !AjaxResult.isSuccess(result)) {
            return F;
        } else if (result.getData() != null) {
            return (RefundReponseEnum) result.getData();
        } else {
            return S;
        }
    }
}
