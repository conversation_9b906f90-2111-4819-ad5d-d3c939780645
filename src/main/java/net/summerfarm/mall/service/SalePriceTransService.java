package net.summerfarm.mall.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.model.vo.EsProductVO;
import net.summerfarm.mall.model.vo.PageVo;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.model.vo.product.HomeProductQueryVo;

import java.util.List;
import java.util.Map;

/**
 * 这是一个适配类，用于计算活动中的商品价格，并重新赋值到sale_price字段
 */
public interface SalePriceTransService {

    PageInfo<ProductInfoVO> selectHomeProductVO2(Integer frontCategoryId, Integer areaNo,
                                                 List<EsProductVO> esProductVOS, Integer adminId, List<String> skuList,
                                                 Integer skuShow, Integer show, Integer direct,
                                                 String msize, String queryStr, Boolean coreCustomer, PageVo pageVo, Integer type, Integer helpOrderFlag);

    /**
     * 新的处理价格信息；适用于已批量查询了活动sku信息
     *
     * @param activitySkuDetailMap
     * @param productInfo
     */
    void handleSalePrice(Map<String, ActivitySkuDetailDTO> activitySkuDetailMap, ProductInfoVO productInfo);

    /**
     * 查询商品信息，处理了商品在活动中时的salePrice字段值以及costPrice字段值，如果该商品在活动中则costPrice则去price字段
     * 商品查询使用商品中心RPC
     */
    List<ProductInfoVO> selectProductInfoByPdIdV2(long pdId, Integer areaNo, Integer adminId,
                                                  List<String> skuList, Integer skuShow, Integer direct);

    /**
     * 首页商品信息，处理了商品在活动中时的salePrice字段值
     *
     * @return
     */
    PageInfo<ProductInfoVO> selectCouponProductVOV3(List<Integer> categoryIds, String pdName, Integer adminId, List<String> skuList,
                                                    Boolean coreCustomer, List<String> blackSkus, PageVo pageVo);
    /**
     * 商品推荐查询，处理了商品在活动中时的salePrice字段值
     */
    List<ProductInfoVO> selectRecommendProductVO(Integer areaNo, List<String> skuList);

    /**
     * 这里处理了库存相关的内容，等到排序完成后再获取保质期相关数据
     */
    List<ProductInfoVO> activityRecommendProduct(Integer areaNo, List<String> skuList);

    /**
     * 处理sku保质期
     */
    void handleProductShelfLife(List<ProductInfoVO> productInfoVOS, Integer areaNo);

    /**
     * 商品推荐查询，处理了商品在活动中时的salePrice字段值
     */
    PageInfo<ProductInfoVO> selectRecommendProductVOV3(Integer areaNo, List<String> skuList, Integer pageNum, Integer pageSize);

    List<ProductInfoVO> selectGroupBuyProductVO(Integer areaNo, List<String> skuList);

    List<ProductInfoVO> selectLandPageFormulaProductVO(Integer areaNo, Long formulaItemId, Boolean coreCustomer);

    List<ProductInfoVO> selectLandPageSkuProductVO(Integer areaNo, Long productId);

    List<ProductInfoVO> selectLandPageSkuAutoSortProductVO(Integer areaNo, Long productId, Boolean coreCustomer);

    List<ProductInfoVO> selectExpiredSkuAutoSortProductVO(Integer areaNo);
    /**
     * 首页商品推荐查询
     *
     * @param esSkuSortMap   商品排序值信息 key为sku，value为排序值
     * @param fixSkus        固定位商品sku
     * @return 商品数据
     */
    PageInfo<ProductInfoVO> selectRecommendProductVOV3(Integer areaNo, Map<String, Object> esSkuSortMap, List<String> fixSkus, PageVo pageVo);

    PageInfo<ProductInfoVO> selectHomeProductVO3(HomeProductQueryVo homeProductQueryVo, PageVo pageVo);

    /**
     * 搜索建议词获取
     *
     * @param homeProductQueryVo 查询参数
     * @return 搜索建议词列表
     */
    List<EsProductVO> querySuggestWordV3(HomeProductQueryVo homeProductQueryVo);
}
