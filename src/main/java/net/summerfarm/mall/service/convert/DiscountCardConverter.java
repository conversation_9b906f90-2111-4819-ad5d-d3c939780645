package net.summerfarm.mall.service.convert;

import net.summerfarm.mall.model.domain.DiscountCardToMerchant;
import net.summerfarm.mall.model.vo.DiscountCardToMerchantVO;

/**
 * <AUTHOR>
 * @Date 2025/1/14 15:32
 * @PackageName:net.summerfarm.mall.service.convert
 * @ClassName: DiscountCardConverter
 * @Description: TODO
 * @Version 1.0
 */
public class DiscountCardConverter {
    public static DiscountCardToMerchantVO toDiscountCardMerchantVO(DiscountCardToMerchant discountCardToMerchant) {
        DiscountCardToMerchantVO merchantVO = new DiscountCardToMerchantVO();
        merchantVO.setId(discountCardToMerchant.getId());
        merchantVO.setMId(discountCardToMerchant.getMId());
        merchantVO.setDiscountCardId(discountCardToMerchant.getDiscountCardId());
        merchantVO.setStatus(discountCardToMerchant.getStatus());
        merchantVO.setTotalTimes(discountCardToMerchant.getTotalTimes());
        merchantVO.setUsedTimes(discountCardToMerchant.getUsedTimes());
        merchantVO.setDeadline(discountCardToMerchant.getDeadline());
        return merchantVO;
    }
}
