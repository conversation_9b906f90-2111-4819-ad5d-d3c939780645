package net.summerfarm.mall.service;

import net.summerfarm.mall.model.dto.combination.*;

import java.util.List;
import java.util.Map;

/**
 * 组合购优化算法服务接口
 * <AUTHOR>
 */
public interface CombinationOptimizationService {

    /**
     * 计算最优组合方案（核心算法）
     * 使用动态规划和贪心算法结合的方式，在性能和效果之间取得平衡
     * 
     * @param activity 组合购活动
     * @param skuList 商品列表
     * @return 最优组合方案列表
     */
    List<CombinationPlanDTO> calculateOptimalPlans(CombinationBuyDTO activity, List<CombinationBuySkuDTO> skuList);

    /**
     * 将商品按池子分组
     * @param activity 组合购活动
     * @param skuList 商品列表
     * @return 按池子分组的商品Map，key为池子编号，value为该池子的商品列表
     */
    Map<String, List<CombinationBuySkuDTO>> groupSkusByPool(CombinationBuyDTO activity, List<CombinationBuySkuDTO> skuList);

    /**
     * 生成所有可能的组合方案
     * 使用回溯算法生成所有满足条件的组合
     * 
     * @param skusByPool 按池子分组的商品
     * @param poolCodes 需要参与组合的池子编号列表
     * @return 所有可能的组合方案
     */
    List<List<CombinationBuySkuDTO>> generateAllCombinations(Map<String, List<CombinationBuySkuDTO>> skusByPool, List<String> poolCodes);

    /**
     * 计算组合方案的优惠金额
     * @param activity 组合购活动
     * @param combination 组合方案
     * @return 优惠金额
     */
    java.math.BigDecimal calculateCombinationDiscount(CombinationBuyDTO activity, List<CombinationBuySkuDTO> combination);

    /**
     * 优化算法：使用贪心策略快速找到近似最优解
     * 适用于商品数量较多的场景，牺牲一定的精确度换取性能
     * 
     * @param activity 组合购活动
     * @param skusByPool 按池子分组的商品
     * @return 近似最优组合方案
     */
    List<CombinationPlanDTO> calculateGreedyOptimalPlans(CombinationBuyDTO activity, Map<String, List<CombinationBuySkuDTO>> skusByPool);

    /**
     * 精确算法：使用动态规划找到最优解
     * 适用于商品数量较少的场景，保证找到最优解
     * 
     * @param activity 组合购活动
     * @param skusByPool 按池子分组的商品
     * @return 最优组合方案
     */
    List<CombinationPlanDTO> calculateExactOptimalPlans(CombinationBuyDTO activity, Map<String, List<CombinationBuySkuDTO>> skusByPool);

    /**
     * 验证组合方案是否有效
     * @param activity 组合购活动
     * @param combination 组合方案
     * @return 是否有效
     */
    boolean isValidCombination(CombinationBuyDTO activity, List<CombinationBuySkuDTO> combination);
}
