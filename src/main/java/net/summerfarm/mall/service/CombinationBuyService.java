package net.summerfarm.mall.service;

import net.summerfarm.mall.model.dto.combination.*;

import java.util.List;

/**
 * 组合购服务接口
 * <AUTHOR>
 */
public interface CombinationBuyService {

    /**
     * 计算组合购优惠
     * @param request 计算请求
     * @return 计算结果
     */
    CombinationCalculateRespDTO calculateCombinationDiscount(CombinationCalculateReqDTO request);

    /**
     * 获取用户可用的组合购活动列表
     * @param mId 用户ID
     * @param areaNo 区域编号
     * @param businessLine 业务线
     * @return 可用活动列表
     */
    List<CombinationBuyDTO> getAvailableActivities(Long mId, Integer areaNo, Integer businessLine);

    /**
     * 根据商品列表匹配可用的组合购活动
     * @param skuList 商品列表
     * @param mId 用户ID
     * @param areaNo 区域编号
     * @param businessLine 业务线
     * @return 匹配的活动列表
     */
    List<CombinationBuyDTO> matchActivitiesBySkus(List<String> skuList, Long mId, Integer areaNo, Integer businessLine);

    /**
     * 计算最优组合方案
     * @param activity 组合购活动
     * @param skuList 商品列表
     * @return 最优组合方案列表
     */
    List<CombinationPlanDTO> calculateOptimalCombinations(CombinationBuyDTO activity, List<CombinationBuySkuDTO> skuList);

    /**
     * 验证组合购使用条件
     * @param activity 组合购活动
     * @param mId 用户ID
     * @param totalAmount 订单总金额
     * @return 是否可用
     */
    boolean validateCombinationUsage(CombinationBuyDTO activity, Long mId, java.math.BigDecimal totalAmount);

    /**
     * 记录组合购使用记录
     * @param combinationBuyId 组合购活动ID
     * @param mId 用户ID
     * @param orderNo 订单号
     * @param plan 使用的组合方案
     */
    void recordCombinationUsage(Long combinationBuyId, Long mId, String orderNo, CombinationPlanDTO plan);

    /**
     * 获取用户组合购使用次数
     * @param mId 用户ID
     * @param combinationBuyId 组合购活动ID
     * @return 使用次数
     */
    int getUserUsageCount(Long mId, Long combinationBuyId);
}
