package net.summerfarm.mall.service.facade;


import net.summerfarm.mall.service.facade.dto.DeliveryRuleDTO;
import net.summerfarm.mall.service.facade.dto.DeliveryRuleQueryInput;
import net.summerfarm.mall.service.facade.dto.PayAfterDeliveryDateReq;
import net.summerfarm.wnc.client.enums.SourceEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface WncDeliveryRuleQueryFacade {

    /**
     * 获取配送规则
     * @param orderTime 下单时间
     * @param merchantId mid
     * @param contactId 地址id
     * @param queryBeginDate 开始时间
     * @param queryEndDate 结束时间
     * @param source 来源
     * @return 可配送日期
     */
    List<LocalDate> getDeliveryRuleList(LocalDateTime orderTime, Long merchantId, Long contactId, LocalDate queryBeginDate, LocalDate queryEndDate, SourceEnum source);

    /**
     * 全品类获取配送规则
     * @param orderTime 下单时间
     * @param merchantId mid
     * @param contactId 地址id
     * @param queryBeginDate 开始时间
     * @param queryEndDate 结束时间
     * @param source 来源
     * @return 可配送日期
     */
    List<LocalDate> getDeliveryRuleListAddSkuList(LocalDateTime orderTime, Long merchantId, Long contactId, LocalDate queryBeginDate, LocalDate queryEndDate, SourceEnum source,List<String> skuCodeList);
    Map<String,LocalDate> getDeliveryRuleListAddSkuMap(LocalDateTime orderTime, Long merchantId, Long contactId, LocalDate queryBeginDate, LocalDate queryEndDate, SourceEnum source, List<String> skuCodeList);


    /**
     * 从缓存中获取配送规则
     * @param orderTime 下单时间
     * @param merchantId mid
     * @param contactId 地址id
     * @param queryBeginDate 开始时间
     * @param queryEndDate 结束时间
     * @param source 来源
     * @return 可配送日期
     */
    DeliveryRuleDTO getDeliveryRuleListByCache(LocalDateTime orderTime, Long merchantId, Long contactId, LocalDate queryBeginDate, LocalDate queryEndDate, SourceEnum source);
    
    /**
     * @description: 查询可配送的日期
     * @author: lzh 
     * @date: 2023/12/12 11:24
     * @param: [deliveryRuleQueryReq]
     * @return: java.time.LocalDate
     **/
    LocalDate queryCloudDeliveryDate(DeliveryRuleQueryInput deliveryRuleQueryReq);

    /**
     * @description: 查询可配送的日期
     * @author: lzh
     * @date: 2023/12/12 11:24
     * @param: [deliveryRuleQueryReq]
     * @return: java.time.LocalDate
     **/
    LocalDate queryPayAfterDeliveryDate(PayAfterDeliveryDateReq payAfterDeliveryDateReq);
}
