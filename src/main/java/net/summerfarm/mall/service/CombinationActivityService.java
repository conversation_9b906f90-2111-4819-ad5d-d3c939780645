package net.summerfarm.mall.service;

import net.summerfarm.mall.model.dto.combination.*;
import net.summerfarm.mall.model.domain.market.ActivityBasicInfo;

import java.util.List;

/**
 * 基于现有Activity表结构的组合购服务接口
 * <AUTHOR>
 */
public interface CombinationActivityService {

    /**
     * 计算组合购优惠
     * @param request 计算请求
     * @return 计算结果
     */
    CombinationCalculateRespDTO calculateCombinationDiscount(CombinationCalculateReqDTO request);

    /**
     * 获取用户可用的组合购活动列表
     * @param mId 用户ID
     * @param areaNo 区域编号
     * @param businessLine 业务线
     * @return 可用活动列表
     */
    List<ActivityBasicInfo> getAvailableCombinationActivities(Long mId, Integer areaNo, Integer businessLine);

    /**
     * 根据商品列表匹配可用的组合购活动
     * @param skuList 商品列表
     * @param mId 用户ID
     * @param areaNo 区域编号
     * @param businessLine 业务线
     * @return 匹配的活动列表
     */
    List<ActivityBasicInfo> matchCombinationActivitiesBySkus(List<String> skuList, Long mId, Integer areaNo, Integer businessLine);

    /**
     * 计算最优组合方案
     * @param activity 组合购活动
     * @param skuList 商品列表
     * @return 最优组合方案列表
     */
    List<CombinationPlanDTO> calculateOptimalCombinations(ActivityBasicInfo activity, List<CombinationBuySkuDTO> skuList);

    /**
     * 验证组合购使用条件
     * @param activity 组合购活动
     * @param mId 用户ID
     * @param totalAmount 订单总金额
     * @return 是否可用
     */
    boolean validateCombinationUsage(ActivityBasicInfo activity, Long mId, java.math.BigDecimal totalAmount);

    /**
     * 记录组合购使用记录
     * @param activityId 活动ID
     * @param mId 用户ID
     * @param orderNo 订单号
     * @param plan 使用的组合方案
     */
    void recordCombinationUsage(Long activityId, Long mId, String orderNo, CombinationPlanDTO plan);

    /**
     * 获取用户组合购使用次数
     * @param mId 用户ID
     * @param activityId 活动ID
     * @return 使用次数
     */
    int getUserUsageCount(Long mId, Long activityId);

    /**
     * 根据活动ID获取活动详情（包含商品池信息）
     * @param activityId 活动ID
     * @return 活动详情
     */
    CombinationBuyDTO getCombinationActivityDetail(Long activityId);

    /**
     * 检查活动是否支持优惠券叠加
     * @param activityId 活动ID
     * @return 是否支持叠加
     */
    boolean isCouponStackable(Long activityId);
}
