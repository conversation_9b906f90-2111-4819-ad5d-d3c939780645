package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CombinationBuyRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 组合购使用记录Mapper
 * <AUTHOR>
 */
@Repository
public interface CombinationBuyRecordMapper {

    /**
     * 插入使用记录
     */
    int insert(CombinationBuyRecord record);

    /**
     * 根据ID查询
     */
    CombinationBuyRecord selectByPrimaryKey(Long id);

    /**
     * 根据订单号查询
     */
    CombinationBuyRecord selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询用户在指定活动的使用次数
     * @param mId 用户ID
     * @param combinationBuyId 组合购活动ID
     * @return 使用次数
     */
    int countByMIdAndCombinationBuyId(@Param("mId") Long mId, 
                                     @Param("combinationBuyId") Long combinationBuyId);

    /**
     * 查询用户在指定时间段内的使用次数
     * @param mId 用户ID
     * @param combinationBuyId 组合购活动ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 使用次数
     */
    int countByMIdAndCombinationBuyIdAndTimeRange(@Param("mId") Long mId,
                                                 @Param("combinationBuyId") Long combinationBuyId,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户的使用记录
     * @param mId 用户ID
     * @param combinationBuyId 组合购活动ID（可选）
     * @return 使用记录列表
     */
    List<CombinationBuyRecord> selectByMId(@Param("mId") Long mId,
                                          @Param("combinationBuyId") Long combinationBuyId);

    /**
     * 查询活动的使用记录
     * @param combinationBuyId 组合购活动ID
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 使用记录列表
     */
    List<CombinationBuyRecord> selectByCombinationBuyId(@Param("combinationBuyId") Long combinationBuyId,
                                                       @Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 统计活动的总使用次数
     * @param combinationBuyId 组合购活动ID
     * @return 总使用次数
     */
    int countByCombinationBuyId(@Param("combinationBuyId") Long combinationBuyId);
}
