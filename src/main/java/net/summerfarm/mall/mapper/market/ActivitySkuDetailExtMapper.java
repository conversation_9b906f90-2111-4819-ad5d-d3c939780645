package net.summerfarm.mall.mapper.market;

import net.summerfarm.mall.model.domain.market.ActivitySkuDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ActivitySkuDetail扩展Mapper（组合购相关）
 * <AUTHOR>
 */
@Repository
public interface ActivitySkuDetailExtMapper {

    /**
     * 根据活动ID查询所有商品详情（包含商品池信息）
     * @param activityId 活动ID
     * @return 商品详情列表
     */
    List<ActivitySkuDetail> selectByActivityId(@Param("activityId") Long activityId);

    /**
     * 根据活动ID和商品池编号查询商品列表
     * @param activityId 活动ID
     * @param poolCode 商品池编号
     * @return 商品列表
     */
    List<ActivitySkuDetail> selectByActivityIdAndPoolCode(@Param("activityId") Long activityId,
                                                         @Param("poolCode") String poolCode);

    /**
     * 根据SKU列表查询相关的活动商品详情
     * @param skuList SKU列表
     * @param activityId 活动ID（可选）
     * @return 商品详情列表
     */
    List<ActivitySkuDetail> selectBySkuList(@Param("skuList") List<String> skuList,
                                          @Param("activityId") Long activityId);

    /**
     * 查询活动的所有商品池编号
     * @param activityId 活动ID
     * @return 商品池编号列表
     */
    List<String> selectPoolCodesByActivityId(@Param("activityId") Long activityId);

    /**
     * 根据活动ID和SKU查询商品详情
     * @param activityId 活动ID
     * @param sku SKU编号
     * @return 商品详情
     */
    ActivitySkuDetail selectByActivityIdAndSku(@Param("activityId") Long activityId,
                                              @Param("sku") String sku);

    /**
     * 查询活动中包含指定SKU的商品池信息
     * @param activityId 活动ID
     * @param skuList SKU列表
     * @return 包含这些SKU的商品池编号列表
     */
    List<String> selectPoolCodesContainingSkus(@Param("activityId") Long activityId,
                                              @Param("skuList") List<String> skuList);

    /**
     * 统计活动中每个商品池的商品数量
     * @param activityId 活动ID
     * @return Map<poolCode, count>
     */
    List<PoolSkuCountDTO> countSkusByPool(@Param("activityId") Long activityId);

    /**
     * 商品池SKU数量DTO
     */
    class PoolSkuCountDTO {
        private String poolCode;
        private Integer skuCount;

        public String getPoolCode() {
            return poolCode;
        }

        public void setPoolCode(String poolCode) {
            this.poolCode = poolCode;
        }

        public Integer getSkuCount() {
            return skuCount;
        }

        public void setSkuCount(Integer skuCount) {
            this.skuCount = skuCount;
        }
    }
}
