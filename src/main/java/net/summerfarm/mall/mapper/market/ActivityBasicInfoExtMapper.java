package net.summerfarm.mall.mapper.market;

import net.summerfarm.mall.model.domain.market.ActivityBasicInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ActivityBasicInfo扩展Mapper（组合购相关）
 * <AUTHOR>
 */
@Repository
public interface ActivityBasicInfoExtMapper {

    /**
     * 查询可用的组合购活动
     * @param activityType 活动类型（组合购）
     * @param areaNo 区域编号
     * @param businessLine 业务线
     * @param currentTime 当前时间
     * @return 可用活动列表
     */
    List<ActivityBasicInfo> selectAvailableCombinationActivities(@Param("activityType") Integer activityType,
                                                               @Param("areaNo") Integer areaNo,
                                                               @Param("businessLine") Integer businessLine,
                                                               @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据SKU列表查询相关的组合购活动
     * @param activityType 活动类型（组合购）
     * @param skuList SKU列表
     * @param areaNo 区域编号
     * @param businessLine 业务线
     * @param currentTime 当前时间
     * @return 相关活动列表
     */
    List<ActivityBasicInfo> selectCombinationActivitiesBySkus(@Param("activityType") Integer activityType,
                                                            @Param("skuList") List<String> skuList,
                                                            @Param("areaNo") Integer areaNo,
                                                            @Param("businessLine") Integer businessLine,
                                                            @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据活动ID查询活动详情（包含couponStackable字段）
     * @param activityId 活动ID
     * @return 活动详情
     */
    ActivityBasicInfo selectCombinationActivityById(@Param("activityId") Long activityId);

    /**
     * 查询活动的商品池数量
     * @param activityId 活动ID
     * @return 商品池数量
     */
    int countPoolsByActivityId(@Param("activityId") Long activityId);

    /**
     * 查询活动是否支持优惠券叠加
     * @param activityId 活动ID
     * @return 是否支持叠加（1-支持，0-不支持）
     */
    Integer selectCouponStackableByActivityId(@Param("activityId") Long activityId);
}
