package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CombinationBuyPoolSku;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 组合购商品池SKU Mapper
 * <AUTHOR>
 */
@Repository
public interface CombinationBuyPoolSkuMapper {

    /**
     * 插入商品池SKU
     */
    int insert(CombinationBuyPoolSku record);

    /**
     * 根据ID查询
     */
    CombinationBuyPoolSku selectByPrimaryKey(Long id);

    /**
     * 更新商品池SKU
     */
    int updateByPrimaryKeySelective(CombinationBuyPoolSku record);

    /**
     * 根据组合购活动ID查询所有商品池SKU
     * @param combinationBuyId 组合购活动ID
     * @return 商品池SKU列表
     */
    List<CombinationBuyPoolSku> selectByCombinationBuyId(@Param("combinationBuyId") Long combinationBuyId);

    /**
     * 根据商品池ID查询SKU列表
     * @param poolId 商品池ID
     * @return SKU列表
     */
    List<CombinationBuyPoolSku> selectByPoolId(@Param("poolId") Long poolId);

    /**
     * 根据SKU列表查询相关的商品池SKU
     * @param skuList SKU列表
     * @param combinationBuyId 组合购活动ID（可选）
     * @return 商品池SKU列表
     */
    List<CombinationBuyPoolSku> selectBySkuList(@Param("skuList") List<String> skuList,
                                               @Param("combinationBuyId") Long combinationBuyId);

    /**
     * 批量插入商品池SKU
     * @param poolSkus 商品池SKU列表
     * @return 插入行数
     */
    int batchInsert(@Param("poolSkus") List<CombinationBuyPoolSku> poolSkus);

    /**
     * 删除商品池的所有SKU
     * @param poolId 商品池ID
     * @return 删除行数
     */
    int deleteByPoolId(@Param("poolId") Long poolId);

    /**
     * 删除组合购活动的所有SKU
     * @param combinationBuyId 组合购活动ID
     * @return 删除行数
     */
    int deleteByCombinationBuyId(@Param("combinationBuyId") Long combinationBuyId);

    /**
     * 根据SKU查询所属的商品池信息
     * @param sku SKU编号
     * @param combinationBuyId 组合购活动ID
     * @return 商品池SKU信息
     */
    CombinationBuyPoolSku selectBySkuAndCombinationBuyId(@Param("sku") String sku, 
                                                        @Param("combinationBuyId") Long combinationBuyId);
}
