package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CombinationBuy;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 组合购活动Mapper
 * <AUTHOR>
 */
@Repository
public interface CombinationBuyMapper {

    /**
     * 插入组合购活动
     */
    int insert(CombinationBuy record);

    /**
     * 根据ID查询
     */
    CombinationBuy selectByPrimaryKey(Long id);

    /**
     * 更新组合购活动
     */
    int updateByPrimaryKeySelective(CombinationBuy record);

    /**
     * 查询可用的组合购活动
     * @param areaNo 区域编号
     * @param userType 用户类型
     * @param businessLine 业务线
     * @param currentTime 当前时间
     * @return 可用活动列表
     */
    List<CombinationBuy> selectAvailableActivities(@Param("areaNo") Integer areaNo, 
                                                   @Param("userType") Integer userType,
                                                   @Param("businessLine") Integer businessLine,
                                                   @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据SKU列表查询相关的组合购活动
     * @param skuList SKU列表
     * @param areaNo 区域编号
     * @param userType 用户类型
     * @param businessLine 业务线
     * @param currentTime 当前时间
     * @return 相关活动列表
     */
    List<CombinationBuy> selectActivitiesBySkus(@Param("skuList") List<String> skuList,
                                               @Param("areaNo") Integer areaNo,
                                               @Param("userType") Integer userType,
                                               @Param("businessLine") Integer businessLine,
                                               @Param("currentTime") LocalDateTime currentTime);

    /**
     * 更新活动使用次数
     * @param id 活动ID
     * @param increment 增量
     * @return 更新行数
     */
    int updateUsedTimes(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 查询活动列表（分页）
     * @param status 状态
     * @param activityName 活动名称（模糊查询）
     * @return 活动列表
     */
    List<CombinationBuy> selectByCondition(@Param("status") Integer status, 
                                          @Param("activityName") String activityName);
}
