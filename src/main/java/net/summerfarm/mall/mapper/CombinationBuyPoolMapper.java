package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CombinationBuyPool;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 组合购商品池Mapper
 * <AUTHOR>
 */
@Repository
public interface CombinationBuyPoolMapper {

    /**
     * 插入商品池
     */
    int insert(CombinationBuyPool record);

    /**
     * 根据ID查询
     */
    CombinationBuyPool selectByPrimaryKey(Long id);

    /**
     * 更新商品池
     */
    int updateByPrimaryKeySelective(CombinationBuyPool record);

    /**
     * 根据组合购活动ID查询商品池列表
     * @param combinationBuyId 组合购活动ID
     * @return 商品池列表
     */
    List<CombinationBuyPool> selectByCombinationBuyId(@Param("combinationBuyId") Long combinationBuyId);

    /**
     * 批量插入商品池
     * @param pools 商品池列表
     * @return 插入行数
     */
    int batchInsert(@Param("pools") List<CombinationBuyPool> pools);

    /**
     * 删除组合购活动的所有商品池
     * @param combinationBuyId 组合购活动ID
     * @return 删除行数
     */
    int deleteByCombinationBuyId(@Param("combinationBuyId") Long combinationBuyId);
}
