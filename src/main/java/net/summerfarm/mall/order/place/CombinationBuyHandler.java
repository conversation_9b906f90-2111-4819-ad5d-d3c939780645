package net.summerfarm.mall.order.place;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.model.dto.combination.CombinationCalculateRespDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import net.summerfarm.mall.service.impl.OrderCalcServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 组合购优惠处理器
 * <AUTHOR>
 */
@Slf4j
@Component
public class CombinationBuyHandler extends AbstractPlaceOrderHandler {

    @Resource
    private OrderCalcServiceImpl orderCalcService;

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();
        List<OrderItemCalcDTO> itemCalcDTOList = orderCalcDTO.getItemCalcDTOList();

        if (placeOrderVO == null || CollectionUtils.isEmpty(itemCalcDTOList)) {
            return false;
        }

        try {
            log.info("开始处理组合购优惠，用户：{}，商品数量：{}", placeOrderVO.getMId(), itemCalcDTOList.size());

            // 计算组合购优惠
            CombinationCalculateRespDTO combinationResult = orderCalcService.calculateCombinationBuyDiscount(itemCalcDTOList, placeOrderVO);

            if (combinationResult == null || !combinationResult.getHasDiscount()) {
                log.info("未找到可用的组合购优惠，用户：{}", placeOrderVO.getMId());
                return false;
            }

            log.info("找到组合购优惠，用户：{}，活动：{}，优惠金额：{}", 
                    placeOrderVO.getMId(), 
                    combinationResult.getActivityName(), 
                    combinationResult.getCombinationDiscountAmount());

            // 应用组合购优惠到订单项
            orderCalcService.applyCombinationBuyDiscount(itemCalcDTOList, combinationResult);

            // 记录组合购信息到PlaceOrderVO（用于后续下单时记录）
            placeOrderVO.setCombinationBuyId(combinationResult.getCombinationBuyId());
            placeOrderVO.setCombinationBuyPlan(JSON.toJSONString(combinationResult.getOptimalPlans().get(0)));

            log.info("组合购优惠处理完成，用户：{}，优惠金额：{}", placeOrderVO.getMId(), combinationResult.getCombinationDiscountAmount());
            return true;

        } catch (Exception e) {
            log.error("处理组合购优惠异常，用户：{}", placeOrderVO.getMId(), e);
            return false;
        }
    }
}
