package net.summerfarm.mall.enums;

/**
 * 组合购活动状态枚举
 * <AUTHOR>
 */
public enum CombinationBuyStatusEnum {

    /**
     * 禁用
     */
    DISABLED(0, "禁用"),

    /**
     * 启用
     */
    ENABLED(1, "启用");

    private final Integer code;
    private final String desc;

    CombinationBuyStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CombinationBuyStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CombinationBuyStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
