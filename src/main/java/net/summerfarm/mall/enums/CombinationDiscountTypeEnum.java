package net.summerfarm.mall.enums;

/**
 * 组合购优惠类型枚举
 * <AUTHOR>
 */
public enum CombinationDiscountTypeEnum {

    /**
     * 折扣率
     */
    DISCOUNT_RATE(1, "折扣率"),

    /**
     * 固定金额
     */
    FIXED_AMOUNT(2, "固定金额");

    private final Integer code;
    private final String desc;

    CombinationDiscountTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CombinationDiscountTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CombinationDiscountTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
