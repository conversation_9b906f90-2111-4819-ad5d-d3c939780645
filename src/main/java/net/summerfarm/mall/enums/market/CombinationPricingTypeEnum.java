package net.summerfarm.mall.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 组合购定价类型枚举
 * 复用ActivityItemConfig的pricingType字段
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CombinationPricingTypeEnum {

    /**
     * 单品定价（原有类型，保持兼容）
     */
    SINGLE_ITEM(0, "单品定价"),

    /**
     * 满金额阶梯（原有类型，保持兼容）
     */
    AMOUNT_LADDER(1, "满金额阶梯"),

    /**
     * 满金额重复（原有类型，保持兼容）
     */
    AMOUNT_REPEAT(2, "满金额重复"),

    /**
     * 满数量重复（原有类型，保持兼容）
     */
    QUANTITY_REPEAT(3, "满数量重复"),

    /**
     * 满数量阶梯（原有类型，保持兼容）
     */
    QUANTITY_LADDER(4, "满数量阶梯"),

    /**
     * 其他（原有类型，保持兼容）
     */
    OTHER(5, "其他"),

    /**
     * 组合购折扣（新增）
     */
    COMBINATION_DISCOUNT(6, "组合购折扣"),

    /**
     * 组合购立减（新增）
     */
    COMBINATION_REDUCE(7, "组合购立减");

    private int code;

    private String value;

    public static CombinationPricingTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CombinationPricingTypeEnum typeEnum : CombinationPricingTypeEnum.values()) {
            if (Objects.equals(code, typeEnum.code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 是否是组合购相关的定价类型
     */
    public static boolean isCombinationType(Integer code) {
        return code != null && (code == COMBINATION_DISCOUNT.getCode() || code == COMBINATION_REDUCE.getCode());
    }

    /**
     * 是否是折扣类型
     */
    public static boolean isDiscountType(Integer code) {
        return code != null && code == COMBINATION_DISCOUNT.getCode();
    }

    /**
     * 是否是立减类型
     */
    public static boolean isReduceType(Integer code) {
        return code != null && code == COMBINATION_REDUCE.getCode();
    }
}
